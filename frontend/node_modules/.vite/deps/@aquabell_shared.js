import {
  __commonJS
} from "./chunk-ROME4SDB.js";

// ../shared/dist/types/index.js
var require_types = __commonJS({
  "../shared/dist/types/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.UnauthorizedError = exports.NotFoundError = exports.ValidationError = exports.AquaBellError = exports.HYDRATION_REMINDERS = exports.DEFAULT_PREFERENCES = exports.DeliveryStatus = exports.NotificationType = void 0;
    var NotificationType;
    (function(NotificationType2) {
      NotificationType2["HYDRATION_REMINDER"] = "hydration_reminder";
      NotificationType2["DAILY_GOAL_REMINDER"] = "daily_goal_reminder";
      NotificationType2["WELCOME"] = "welcome";
      NotificationType2["ACHIEVEMENT"] = "achievement";
    })(NotificationType || (exports.NotificationType = NotificationType = {}));
    var DeliveryStatus;
    (function(DeliveryStatus2) {
      DeliveryStatus2["PENDING"] = "pending";
      DeliveryStatus2["SENT"] = "sent";
      DeliveryStatus2["DELIVERED"] = "delivered";
      DeliveryStatus2["FAILED"] = "failed";
      DeliveryStatus2["RETRY"] = "retry";
    })(DeliveryStatus || (exports.DeliveryStatus = DeliveryStatus = {}));
    exports.DEFAULT_PREFERENCES = {
      daily_goal_ml: 2e3,
      // 2 liters
      wake_time: "07:00",
      sleep_time: "22:00",
      reminder_interval_minutes: 60,
      // 1 hour
      is_notifications_enabled: true
    };
    exports.HYDRATION_REMINDERS = [
      {
        title: "Time to hydrate! 💧",
        body: "Your body is calling for some refreshing water!",
        emoji: "💧"
      },
      {
        title: "Water break! 🌊",
        body: "Take a moment to drink some water and feel refreshed!",
        emoji: "🌊"
      },
      {
        title: "Hydration station! 🚰",
        body: "Keep your energy up with a nice glass of water!",
        emoji: "🚰"
      },
      {
        title: "Drink up, buttercup! 🌻",
        body: "Your future self will thank you for staying hydrated!",
        emoji: "🌻"
      },
      {
        title: "H2O time! 💦",
        body: "Let's keep those hydration levels topped up!",
        emoji: "💦"
      }
    ];
    var AquaBellError = class extends Error {
      constructor(message, code, statusCode = 500) {
        super(message);
        this.code = code;
        this.statusCode = statusCode;
        this.name = "AquaBellError";
      }
    };
    exports.AquaBellError = AquaBellError;
    var ValidationError = class extends AquaBellError {
      constructor(message, field) {
        super(message, "VALIDATION_ERROR", 400);
        this.name = "ValidationError";
      }
    };
    exports.ValidationError = ValidationError;
    var NotFoundError = class extends AquaBellError {
      constructor(resource) {
        super(`${resource} not found`, "NOT_FOUND", 404);
        this.name = "NotFoundError";
      }
    };
    exports.NotFoundError = NotFoundError;
    var UnauthorizedError = class extends AquaBellError {
      constructor(message = "Unauthorized") {
        super(message, "UNAUTHORIZED", 401);
        this.name = "UnauthorizedError";
      }
    };
    exports.UnauthorizedError = UnauthorizedError;
  }
});

// ../shared/dist/utils/index.js
var require_utils = __commonJS({
  "../shared/dist/utils/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isWithinActiveHours = isWithinActiveHours;
    exports.getNextReminderTime = getNextReminderTime;
    exports.formatTime = formatTime;
    exports.isValidTimeFormat = isValidTimeFormat;
    exports.formatVolume = formatVolume;
    exports.getRandomReminder = getRandomReminder;
    exports.isValidEmail = isValidEmail;
    exports.sanitizeString = sanitizeString;
    exports.calculateProgress = calculateProgress;
    exports.debounce = debounce;
    exports.sleep = sleep;
    exports.generateUUID = generateUUID;
    function isWithinActiveHours(wakeTime, sleepTime, timezone = "UTC", currentTime) {
      const now = currentTime || /* @__PURE__ */ new Date();
      const userTime = new Date(now.toLocaleString("en-US", { timeZone: timezone }));
      const currentHour = userTime.getHours();
      const currentMinute = userTime.getMinutes();
      const currentTimeInMinutes = currentHour * 60 + currentMinute;
      const [wakeHour, wakeMinute] = wakeTime.split(":").map(Number);
      const [sleepHour, sleepMinute] = sleepTime.split(":").map(Number);
      const wakeTimeInMinutes = wakeHour * 60 + wakeMinute;
      const sleepTimeInMinutes = sleepHour * 60 + sleepMinute;
      if (sleepTimeInMinutes > wakeTimeInMinutes) {
        return currentTimeInMinutes >= wakeTimeInMinutes && currentTimeInMinutes <= sleepTimeInMinutes;
      } else {
        return currentTimeInMinutes >= wakeTimeInMinutes || currentTimeInMinutes <= sleepTimeInMinutes;
      }
    }
    function getNextReminderTime(lastReminderTime, intervalMinutes, wakeTime, sleepTime, timezone = "UTC") {
      const nextTime = new Date(lastReminderTime.getTime() + intervalMinutes * 60 * 1e3);
      if (!isWithinActiveHours(wakeTime, sleepTime, timezone, nextTime)) {
        const tomorrow = new Date(nextTime);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const [wakeHour, wakeMinute] = wakeTime.split(":").map(Number);
        tomorrow.setHours(wakeHour, wakeMinute, 0, 0);
        return tomorrow;
      }
      return nextTime;
    }
    function formatTime(time) {
      const [hours, minutes] = time.split(":");
      return `${hours.padStart(2, "0")}:${minutes.padStart(2, "0")}`;
    }
    function isValidTimeFormat(time) {
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
      return timeRegex.test(time);
    }
    function formatVolume(ml) {
      if (ml >= 1e3) {
        return `${(ml / 1e3).toFixed(1)}L`;
      }
      return `${ml}ml`;
    }
    function getRandomReminder() {
      const reminders = [
        {
          title: "Time to hydrate! 💧",
          body: "Your body is calling for some refreshing water!",
          emoji: "💧"
        },
        {
          title: "Water break! 🌊",
          body: "Take a moment to drink some water and feel refreshed!",
          emoji: "🌊"
        },
        {
          title: "Hydration station! 🚰",
          body: "Keep your energy up with a nice glass of water!",
          emoji: "🚰"
        },
        {
          title: "Drink up, buttercup! 🌻",
          body: "Your future self will thank you for staying hydrated!",
          emoji: "🌻"
        },
        {
          title: "H2O time! 💦",
          body: "Let's keep those hydration levels topped up!",
          emoji: "💦"
        },
        {
          title: "Splash of wellness! 🌈",
          body: "Every sip brings you closer to your daily goal!",
          emoji: "🌈"
        },
        {
          title: "Aqua alert! 🔔",
          body: "Time for a refreshing water moment!",
          emoji: "🔔"
        },
        {
          title: "Hydration hero! 🦸‍♀️",
          body: "Be your own superhero - drink some water!",
          emoji: "🦸‍♀️"
        }
      ];
      return reminders[Math.floor(Math.random() * reminders.length)];
    }
    function isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    }
    function sanitizeString(input) {
      return input.trim().replace(/[<>]/g, "");
    }
    function calculateProgress(current, goal) {
      if (goal <= 0)
        return 0;
      return Math.min(Math.round(current / goal * 100), 100);
    }
    function debounce(func, wait) {
      let timeout;
      return (...args) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
      };
    }
    function sleep(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms));
    }
    function generateUUID() {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === "x" ? r : r & 3 | 8;
        return v.toString(16);
      });
    }
  }
});

// ../shared/dist/index.js
var require_dist = __commonJS({
  "../shared/dist/index.js"(exports) {
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0)
        k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0)
        k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m)
        if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p))
          __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_types(), exports);
    __exportStar(require_utils(), exports);
  }
});
export default require_dist();
//# sourceMappingURL=@aquabell_shared.js.map
