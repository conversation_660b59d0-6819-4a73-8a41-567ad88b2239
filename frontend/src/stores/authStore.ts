import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { User } from '@supabase/supabase-js'
import { auth, userProfile } from '../services/supabase'
import type { Database } from '../types/supabase'

type UserProfile = Database['public']['Tables']['users']['Row']

interface AuthState {
  user: User | null
  profile: UserProfile | null
  loading: boolean
  error: string | null
}

interface AuthActions {
  initialize: () => Promise<void>
  signInAnonymously: () => Promise<void>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>
  clearError: () => void
}

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      profile: null,
      loading: true,
      error: null,

      // Actions
      initialize: async () => {
        try {
          set({ loading: true, error: null })
          
          const session = await auth.getSession()
          
          if (session?.user) {
            const profile = await userProfile.get(session.user.id)
            set({ 
              user: session.user, 
              profile,
              loading: false 
            })
          } else {
            set({ 
              user: null, 
              profile: null,
              loading: false 
            })
          }
        } catch (error) {
          console.error('Auth initialization error:', error)
          set({ 
            error: error instanceof Error ? error.message : 'Authentication failed',
            loading: false 
          })
        }
      },

      signInAnonymously: async () => {
        try {
          set({ loading: true, error: null })
          
          const { user } = await auth.signInAnonymously()
          
          if (user) {
            // Get or create user profile
            try {
              const profile = await userProfile.get(user.id)
              set({ 
                user, 
                profile,
                loading: false 
              })
            } catch (profileError) {
              // Profile might not exist yet, it will be created by the trigger
              console.log('Profile not found, will be created by trigger')
              set({ 
                user, 
                profile: null,
                loading: false 
              })
            }
          }
        } catch (error) {
          console.error('Sign in error:', error)
          set({ 
            error: error instanceof Error ? error.message : 'Sign in failed',
            loading: false 
          })
        }
      },

      signOut: async () => {
        try {
          set({ loading: true, error: null })
          
          await auth.signOut()
          
          set({ 
            user: null, 
            profile: null,
            loading: false 
          })
        } catch (error) {
          console.error('Sign out error:', error)
          set({ 
            error: error instanceof Error ? error.message : 'Sign out failed',
            loading: false 
          })
        }
      },

      updateProfile: async (updates) => {
        try {
          const { user } = get()
          if (!user) throw new Error('No user logged in')

          set({ loading: true, error: null })
          
          const updatedProfile = await userProfile.update(user.id, updates)
          
          set({ 
            profile: updatedProfile,
            loading: false 
          })
        } catch (error) {
          console.error('Profile update error:', error)
          set({ 
            error: error instanceof Error ? error.message : 'Profile update failed',
            loading: false 
          })
        }
      },

      clearError: () => {
        set({ error: null })
      },

      // Test function to verify local backend connection
      testConnection: async () => {
        try {
          console.log('🧪 Testing Supabase connection...')
          const session = await auth.getSession()
          console.log('✅ Supabase connection test successful:', {
            hasSession: !!session,
            sessionExists: session !== null
          })
          return true
        } catch (error) {
          console.error('❌ Supabase connection test failed:', error)
          return false
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        profile: state.profile
      })
    }
  )
)
