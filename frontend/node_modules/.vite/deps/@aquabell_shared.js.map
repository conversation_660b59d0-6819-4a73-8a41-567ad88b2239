{"version": 3, "sources": ["../../../../shared/dist/types/index.js", "../../../../shared/dist/utils/index.js", "../../../../shared/dist/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.UnauthorizedError = exports.NotFoundError = exports.ValidationError = exports.AquaBellError = exports.HYDRATION_REMINDERS = exports.DEFAULT_PREFERENCES = exports.DeliveryStatus = exports.NotificationType = void 0;\nvar NotificationType;\n(function (NotificationType) {\n    NotificationType[\"HYDRATION_REMINDER\"] = \"hydration_reminder\";\n    NotificationType[\"DAILY_GOAL_REMINDER\"] = \"daily_goal_reminder\";\n    NotificationType[\"WELCOME\"] = \"welcome\";\n    NotificationType[\"ACHIEVEMENT\"] = \"achievement\";\n})(NotificationType || (exports.NotificationType = NotificationType = {}));\nvar DeliveryStatus;\n(function (DeliveryStatus) {\n    DeliveryStatus[\"PENDING\"] = \"pending\";\n    DeliveryStatus[\"SENT\"] = \"sent\";\n    DeliveryStatus[\"DELIVERED\"] = \"delivered\";\n    DeliveryStatus[\"FAILED\"] = \"failed\";\n    DeliveryStatus[\"RETRY\"] = \"retry\";\n})(DeliveryStatus || (exports.DeliveryStatus = DeliveryStatus = {}));\n// Constants\nexports.DEFAULT_PREFERENCES = {\n    daily_goal_ml: 2000, // 2 liters\n    wake_time: '07:00',\n    sleep_time: '22:00',\n    reminder_interval_minutes: 60, // 1 hour\n    is_notifications_enabled: true\n};\nexports.HYDRATION_REMINDERS = [\n    {\n        title: \"Time to hydrate! 💧\",\n        body: \"Your body is calling for some refreshing water!\",\n        emoji: \"💧\"\n    },\n    {\n        title: \"Water break! 🌊\",\n        body: \"Take a moment to drink some water and feel refreshed!\",\n        emoji: \"🌊\"\n    },\n    {\n        title: \"Hydration station! 🚰\",\n        body: \"Keep your energy up with a nice glass of water!\",\n        emoji: \"🚰\"\n    },\n    {\n        title: \"Drink up, buttercup! 🌻\",\n        body: \"Your future self will thank you for staying hydrated!\",\n        emoji: \"🌻\"\n    },\n    {\n        title: \"H2O time! 💦\",\n        body: \"Let's keep those hydration levels topped up!\",\n        emoji: \"💦\"\n    }\n];\n// Error Types\nclass AquaBellError extends Error {\n    constructor(message, code, statusCode = 500) {\n        super(message);\n        this.code = code;\n        this.statusCode = statusCode;\n        this.name = 'AquaBellError';\n    }\n}\nexports.AquaBellError = AquaBellError;\nclass ValidationError extends AquaBellError {\n    constructor(message, field) {\n        super(message, 'VALIDATION_ERROR', 400);\n        this.name = 'ValidationError';\n    }\n}\nexports.ValidationError = ValidationError;\nclass NotFoundError extends AquaBellError {\n    constructor(resource) {\n        super(`${resource} not found`, 'NOT_FOUND', 404);\n        this.name = 'NotFoundError';\n    }\n}\nexports.NotFoundError = NotFoundError;\nclass UnauthorizedError extends AquaBellError {\n    constructor(message = 'Unauthorized') {\n        super(message, 'UNAUTHORIZED', 401);\n        this.name = 'UnauthorizedError';\n    }\n}\nexports.UnauthorizedError = UnauthorizedError;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isWithinActiveHours = isWithinActiveHours;\nexports.getNextReminderTime = getNextReminderTime;\nexports.formatTime = formatTime;\nexports.isValidTimeFormat = isValidTimeFormat;\nexports.formatVolume = formatVolume;\nexports.getRandomReminder = getRandomReminder;\nexports.isValidEmail = isValidEmail;\nexports.sanitizeString = sanitizeString;\nexports.calculateProgress = calculateProgress;\nexports.debounce = debounce;\nexports.sleep = sleep;\nexports.generateUUID = generateUUID;\n/**\n * Check if current time is within user's active hours\n */\nfunction isWithinActiveHours(wakeTime, sleepTime, timezone = 'UTC', currentTime) {\n    const now = currentTime || new Date();\n    // Convert to user's timezone\n    const userTime = new Date(now.toLocaleString(\"en-US\", { timeZone: timezone }));\n    const currentHour = userTime.getHours();\n    const currentMinute = userTime.getMinutes();\n    const currentTimeInMinutes = currentHour * 60 + currentMinute;\n    // Parse wake and sleep times\n    const [wakeHour, wakeMinute] = wakeTime.split(':').map(Number);\n    const [sleepHour, sleepMinute] = sleepTime.split(':').map(Number);\n    const wakeTimeInMinutes = wakeHour * 60 + wakeMinute;\n    const sleepTimeInMinutes = sleepHour * 60 + sleepMinute;\n    // Handle case where sleep time is next day (e.g., wake: 07:00, sleep: 23:00)\n    if (sleepTimeInMinutes > wakeTimeInMinutes) {\n        return currentTimeInMinutes >= wakeTimeInMinutes && currentTimeInMinutes <= sleepTimeInMinutes;\n    }\n    else {\n        // Handle case where sleep time crosses midnight (e.g., wake: 22:00, sleep: 06:00)\n        return currentTimeInMinutes >= wakeTimeInMinutes || currentTimeInMinutes <= sleepTimeInMinutes;\n    }\n}\n/**\n * Calculate next reminder time based on interval and active hours\n */\nfunction getNextReminderTime(lastReminderTime, intervalMinutes, wakeTime, sleepTime, timezone = 'UTC') {\n    const nextTime = new Date(lastReminderTime.getTime() + intervalMinutes * 60 * 1000);\n    // If next time is outside active hours, schedule for next wake time\n    if (!isWithinActiveHours(wakeTime, sleepTime, timezone, nextTime)) {\n        const tomorrow = new Date(nextTime);\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        const [wakeHour, wakeMinute] = wakeTime.split(':').map(Number);\n        tomorrow.setHours(wakeHour, wakeMinute, 0, 0);\n        return tomorrow;\n    }\n    return nextTime;\n}\n/**\n * Format time string to HH:MM format\n */\nfunction formatTime(time) {\n    const [hours, minutes] = time.split(':');\n    return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;\n}\n/**\n * Validate time string format (HH:MM)\n */\nfunction isValidTimeFormat(time) {\n    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;\n    return timeRegex.test(time);\n}\n/**\n * Convert milliliters to user-friendly format\n */\nfunction formatVolume(ml) {\n    if (ml >= 1000) {\n        return `${(ml / 1000).toFixed(1)}L`;\n    }\n    return `${ml}ml`;\n}\n/**\n * Generate a random hydration reminder from predefined list\n */\nfunction getRandomReminder() {\n    const reminders = [\n        {\n            title: \"Time to hydrate! 💧\",\n            body: \"Your body is calling for some refreshing water!\",\n            emoji: \"💧\"\n        },\n        {\n            title: \"Water break! 🌊\",\n            body: \"Take a moment to drink some water and feel refreshed!\",\n            emoji: \"🌊\"\n        },\n        {\n            title: \"Hydration station! 🚰\",\n            body: \"Keep your energy up with a nice glass of water!\",\n            emoji: \"🚰\"\n        },\n        {\n            title: \"Drink up, buttercup! 🌻\",\n            body: \"Your future self will thank you for staying hydrated!\",\n            emoji: \"🌻\"\n        },\n        {\n            title: \"H2O time! 💦\",\n            body: \"Let's keep those hydration levels topped up!\",\n            emoji: \"💦\"\n        },\n        {\n            title: \"Splash of wellness! 🌈\",\n            body: \"Every sip brings you closer to your daily goal!\",\n            emoji: \"🌈\"\n        },\n        {\n            title: \"Aqua alert! 🔔\",\n            body: \"Time for a refreshing water moment!\",\n            emoji: \"🔔\"\n        },\n        {\n            title: \"Hydration hero! 🦸‍♀️\",\n            body: \"Be your own superhero - drink some water!\",\n            emoji: \"🦸‍♀️\"\n        }\n    ];\n    return reminders[Math.floor(Math.random() * reminders.length)];\n}\n/**\n * Validate email format\n */\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Sanitize user input\n */\nfunction sanitizeString(input) {\n    return input.trim().replace(/[<>]/g, '');\n}\n/**\n * Calculate progress percentage\n */\nfunction calculateProgress(current, goal) {\n    if (goal <= 0)\n        return 0;\n    return Math.min(Math.round((current / goal) * 100), 100);\n}\n/**\n * Debounce function for API calls\n */\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args) => {\n        clearTimeout(timeout);\n        timeout = setTimeout(() => func(...args), wait);\n    };\n}\n/**\n * Sleep utility for async operations\n */\nfunction sleep(ms) {\n    return new Promise(resolve => setTimeout(resolve, ms));\n}\n/**\n * Generate UUID v4\n */\nfunction generateUUID() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        const r = Math.random() * 16 | 0;\n        const v = c === 'x' ? r : (r & 0x3 | 0x8);\n        return v.toString(16);\n    });\n}\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\n// Export all types\n__exportStar(require(\"./types\"), exports);\n// Export all utilities\n__exportStar(require(\"./utils\"), exports);\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,oBAAoB,QAAQ,gBAAgB,QAAQ,kBAAkB,QAAQ,gBAAgB,QAAQ,sBAAsB,QAAQ,sBAAsB,QAAQ,iBAAiB,QAAQ,mBAAmB;AACtN,QAAI;AACJ,KAAC,SAAUA,mBAAkB;AACzB,MAAAA,kBAAiB,oBAAoB,IAAI;AACzC,MAAAA,kBAAiB,qBAAqB,IAAI;AAC1C,MAAAA,kBAAiB,SAAS,IAAI;AAC9B,MAAAA,kBAAiB,aAAa,IAAI;AAAA,IACtC,GAAG,qBAAqB,QAAQ,mBAAmB,mBAAmB,CAAC,EAAE;AACzE,QAAI;AACJ,KAAC,SAAUC,iBAAgB;AACvB,MAAAA,gBAAe,SAAS,IAAI;AAC5B,MAAAA,gBAAe,MAAM,IAAI;AACzB,MAAAA,gBAAe,WAAW,IAAI;AAC9B,MAAAA,gBAAe,QAAQ,IAAI;AAC3B,MAAAA,gBAAe,OAAO,IAAI;AAAA,IAC9B,GAAG,mBAAmB,QAAQ,iBAAiB,iBAAiB,CAAC,EAAE;AAEnE,YAAQ,sBAAsB;AAAA,MAC1B,eAAe;AAAA;AAAA,MACf,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,2BAA2B;AAAA;AAAA,MAC3B,0BAA0B;AAAA,IAC9B;AACA,YAAQ,sBAAsB;AAAA,MAC1B;AAAA,QACI,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AAAA,IACJ;AAEA,QAAM,gBAAN,cAA4B,MAAM;AAAA,MAC9B,YAAY,SAAS,MAAM,aAAa,KAAK;AACzC,cAAM,OAAO;AACb,aAAK,OAAO;AACZ,aAAK,aAAa;AAClB,aAAK,OAAO;AAAA,MAChB;AAAA,IACJ;AACA,YAAQ,gBAAgB;AACxB,QAAM,kBAAN,cAA8B,cAAc;AAAA,MACxC,YAAY,SAAS,OAAO;AACxB,cAAM,SAAS,oBAAoB,GAAG;AACtC,aAAK,OAAO;AAAA,MAChB;AAAA,IACJ;AACA,YAAQ,kBAAkB;AAC1B,QAAM,gBAAN,cAA4B,cAAc;AAAA,MACtC,YAAY,UAAU;AAClB,cAAM,GAAG,QAAQ,cAAc,aAAa,GAAG;AAC/C,aAAK,OAAO;AAAA,MAChB;AAAA,IACJ;AACA,YAAQ,gBAAgB;AACxB,QAAM,oBAAN,cAAgC,cAAc;AAAA,MAC1C,YAAY,UAAU,gBAAgB;AAClC,cAAM,SAAS,gBAAgB,GAAG;AAClC,aAAK,OAAO;AAAA,MAChB;AAAA,IACJ;AACA,YAAQ,oBAAoB;AAAA;AAAA;;;ACnF5B;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,sBAAsB;AAC9B,YAAQ,sBAAsB;AAC9B,YAAQ,aAAa;AACrB,YAAQ,oBAAoB;AAC5B,YAAQ,eAAe;AACvB,YAAQ,oBAAoB;AAC5B,YAAQ,eAAe;AACvB,YAAQ,iBAAiB;AACzB,YAAQ,oBAAoB;AAC5B,YAAQ,WAAW;AACnB,YAAQ,QAAQ;AAChB,YAAQ,eAAe;AAIvB,aAAS,oBAAoB,UAAU,WAAW,WAAW,OAAO,aAAa;AAC7E,YAAM,MAAM,eAAe,oBAAI,KAAK;AAEpC,YAAM,WAAW,IAAI,KAAK,IAAI,eAAe,SAAS,EAAE,UAAU,SAAS,CAAC,CAAC;AAC7E,YAAM,cAAc,SAAS,SAAS;AACtC,YAAM,gBAAgB,SAAS,WAAW;AAC1C,YAAM,uBAAuB,cAAc,KAAK;AAEhD,YAAM,CAAC,UAAU,UAAU,IAAI,SAAS,MAAM,GAAG,EAAE,IAAI,MAAM;AAC7D,YAAM,CAAC,WAAW,WAAW,IAAI,UAAU,MAAM,GAAG,EAAE,IAAI,MAAM;AAChE,YAAM,oBAAoB,WAAW,KAAK;AAC1C,YAAM,qBAAqB,YAAY,KAAK;AAE5C,UAAI,qBAAqB,mBAAmB;AACxC,eAAO,wBAAwB,qBAAqB,wBAAwB;AAAA,MAChF,OACK;AAED,eAAO,wBAAwB,qBAAqB,wBAAwB;AAAA,MAChF;AAAA,IACJ;AAIA,aAAS,oBAAoB,kBAAkB,iBAAiB,UAAU,WAAW,WAAW,OAAO;AACnG,YAAM,WAAW,IAAI,KAAK,iBAAiB,QAAQ,IAAI,kBAAkB,KAAK,GAAI;AAElF,UAAI,CAAC,oBAAoB,UAAU,WAAW,UAAU,QAAQ,GAAG;AAC/D,cAAM,WAAW,IAAI,KAAK,QAAQ;AAClC,iBAAS,QAAQ,SAAS,QAAQ,IAAI,CAAC;AACvC,cAAM,CAAC,UAAU,UAAU,IAAI,SAAS,MAAM,GAAG,EAAE,IAAI,MAAM;AAC7D,iBAAS,SAAS,UAAU,YAAY,GAAG,CAAC;AAC5C,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAIA,aAAS,WAAW,MAAM;AACtB,YAAM,CAAC,OAAO,OAAO,IAAI,KAAK,MAAM,GAAG;AACvC,aAAO,GAAG,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,QAAQ,SAAS,GAAG,GAAG,CAAC;AAAA,IAChE;AAIA,aAAS,kBAAkB,MAAM;AAC7B,YAAM,YAAY;AAClB,aAAO,UAAU,KAAK,IAAI;AAAA,IAC9B;AAIA,aAAS,aAAa,IAAI;AACtB,UAAI,MAAM,KAAM;AACZ,eAAO,IAAI,KAAK,KAAM,QAAQ,CAAC,CAAC;AAAA,MACpC;AACA,aAAO,GAAG,EAAE;AAAA,IAChB;AAIA,aAAS,oBAAoB;AACzB,YAAM,YAAY;AAAA,QACd;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACX;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACX;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACX;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACX;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACX;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACX;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACX;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO,UAAU,KAAK,MAAM,KAAK,OAAO,IAAI,UAAU,MAAM,CAAC;AAAA,IACjE;AAIA,aAAS,aAAa,OAAO;AACzB,YAAM,aAAa;AACnB,aAAO,WAAW,KAAK,KAAK;AAAA,IAChC;AAIA,aAAS,eAAe,OAAO;AAC3B,aAAO,MAAM,KAAK,EAAE,QAAQ,SAAS,EAAE;AAAA,IAC3C;AAIA,aAAS,kBAAkB,SAAS,MAAM;AACtC,UAAI,QAAQ;AACR,eAAO;AACX,aAAO,KAAK,IAAI,KAAK,MAAO,UAAU,OAAQ,GAAG,GAAG,GAAG;AAAA,IAC3D;AAIA,aAAS,SAAS,MAAM,MAAM;AAC1B,UAAI;AACJ,aAAO,IAAI,SAAS;AAChB,qBAAa,OAAO;AACpB,kBAAU,WAAW,MAAM,KAAK,GAAG,IAAI,GAAG,IAAI;AAAA,MAClD;AAAA,IACJ;AAIA,aAAS,MAAM,IAAI;AACf,aAAO,IAAI,QAAQ,aAAW,WAAW,SAAS,EAAE,CAAC;AAAA,IACzD;AAIA,aAAS,eAAe;AACpB,aAAO,uCAAuC,QAAQ,SAAS,SAAU,GAAG;AACxE,cAAM,IAAI,KAAK,OAAO,IAAI,KAAK;AAC/B,cAAM,IAAI,MAAM,MAAM,IAAK,IAAI,IAAM;AACrC,eAAO,EAAE,SAAS,EAAE;AAAA,MACxB,CAAC;AAAA,IACL;AAAA;AAAA;;;AC1KA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO;AAAW,aAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO;AAAW,aAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAS,GAAGC,UAAS;AACnE,eAAS,KAAK;AAAG,YAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAKA,UAAS,CAAC;AAAG,0BAAgBA,UAAS,GAAG,CAAC;AAAA,IAC5H;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,iBAAa,iBAAoB,OAAO;AAExC,iBAAa,iBAAoB,OAAO;AAAA;AAAA;", "names": ["NotificationType", "DeliveryStatus", "exports"]}