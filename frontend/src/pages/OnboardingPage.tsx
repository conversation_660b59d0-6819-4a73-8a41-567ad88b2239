import { useState, useEffect } from 'react'
import { useAuthStore } from '../stores/authStore'
import LoadingSpinner from '../components/LoadingSpinner'

export default function OnboardingPage() {
  const { signInAnonymously, loading, error, testConnection } = useAuthStore()
  const [step, setStep] = useState(0)

  // Test connection on component mount (development only)
  useEffect(() => {
    if (import.meta.env.DEV) {
      testConnection()
    }
  }, [])

  const handleGetStarted = async () => {
    await signInAnonymously()
  }

  const onboardingSteps = [
    {
      emoji: '💧',
      title: 'Welcome to AquaBell!',
      description: 'Your friendly companion for staying hydrated throughout the day',
      action: () => setStep(1)
    },
    {
      emoji: '🔔',
      title: 'Smart Reminders',
      description: 'Get personalized notifications during your active hours to drink water',
      action: () => setStep(2)
    },
    {
      emoji: '🎯',
      title: 'Set Your Goals',
      description: 'Customize your daily water intake goals and reminder preferences',
      action: () => setStep(3)
    },
    {
      emoji: '🌟',
      title: 'Ready to Start?',
      description: 'Let\'s begin your hydration journey together!',
      action: handleGetStarted
    }
  ]

  const currentStep = onboardingSteps[step]

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="card text-center">
          {/* Progress indicator */}
          <div className="flex justify-center mb-8">
            {onboardingSteps.map((_, index) => (
              <div
                key={index}
                className={`w-3 h-3 rounded-full mx-1 transition-colors duration-300 ${
                  index <= step ? 'bg-primary-500' : 'bg-gray-200'
                }`}
              />
            ))}
          </div>

          {/* Step content */}
          <div className="mb-8">
            <div className="text-6xl mb-4 animate-bounce-slow">
              {currentStep.emoji}
            </div>
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              {currentStep.title}
            </h1>
            <p className="text-gray-600 leading-relaxed">
              {currentStep.description}
            </p>
          </div>

          {/* Error message */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-2xl">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Action button */}
          <button
            onClick={currentStep.action}
            disabled={loading}
            className="btn-primary w-full flex items-center justify-center gap-2"
          >
            {loading && <LoadingSpinner size="small" />}
            {step < 3 ? 'Next' : 'Get Started'}
          </button>

          {/* Skip option for first steps */}
          {step < 3 && (
            <button
              onClick={() => setStep(3)}
              className="mt-4 text-gray-500 hover:text-gray-700 text-sm transition-colors duration-200"
            >
              Skip intro
            </button>
          )}
        </div>

        {/* Features preview */}
        <div className="mt-8 grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl mb-2">⏰</div>
            <p className="text-xs text-gray-600">Smart Timing</p>
          </div>
          <div className="text-center">
            <div className="text-2xl mb-2">📱</div>
            <p className="text-xs text-gray-600">Mobile Friendly</p>
          </div>
          <div className="text-center">
            <div className="text-2xl mb-2">🎨</div>
            <p className="text-xs text-gray-600">Beautiful UI</p>
          </div>
        </div>
      </div>
    </div>
  )
}
