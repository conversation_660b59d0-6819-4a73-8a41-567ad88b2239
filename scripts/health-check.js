#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 AquaBell Health Check\n');

let hasErrors = false;

function checkItem(name, check, fix) {
  try {
    const result = check();
    if (result) {
      console.log(`✅ ${name}`);
      return true;
    } else {
      console.log(`❌ ${name}`);
      if (fix) {
        console.log(`   Fix: ${fix}`);
      }
      hasErrors = true;
      return false;
    }
  } catch (error) {
    console.log(`❌ ${name} - Error: ${error.message}`);
    if (fix) {
      console.log(`   Fix: ${fix}`);
    }
    hasErrors = true;
    return false;
  }
}

// Check 1: Shared package built
checkItem(
  'Shared package built',
  () => fs.existsSync('shared/dist/index.js') && fs.existsSync('shared/dist/index.d.ts'),
  'cd shared && npm run build'
);

// Check 2: Node modules installed
checkItem(
  'Root dependencies installed',
  () => fs.existsSync('node_modules'),
  'npm install'
);

checkItem(
  'Frontend dependencies installed',
  () => fs.existsSync('frontend/node_modules'),
  'cd frontend && npm install'
);

checkItem(
  'Shared dependencies installed',
  () => fs.existsSync('shared/node_modules') || fs.existsSync('node_modules/@aquabell'),
  'npm run install:all'
);

// Check 3: Environment files
checkItem(
  'Frontend environment file exists',
  () => fs.existsSync('frontend/.env'),
  'cp frontend/.env.example frontend/.env'
);

checkItem(
  'Backend environment file exists',
  () => fs.existsSync('backend/.env'),
  'cp backend/.env.example backend/.env'
);

// Check 4: Required files
checkItem(
  'Service worker exists',
  () => fs.existsSync('frontend/public/sw.js'),
  'File should exist - check repository'
);

checkItem(
  'Firebase service worker exists',
  () => fs.existsSync('frontend/public/firebase-messaging-sw.js'),
  'File should exist - check repository'
);

// Check 5: Icons (basic check)
checkItem(
  'App icons exist',
  () => fs.existsSync('frontend/public/icons') && fs.readdirSync('frontend/public/icons').length > 0,
  'mkdir -p frontend/public/icons && create placeholder icons'
);

// Check 6: TypeScript configuration
checkItem(
  'Frontend TypeScript config',
  () => fs.existsSync('frontend/tsconfig.json'),
  'File should exist - check repository'
);

checkItem(
  'Shared TypeScript config',
  () => fs.existsSync('shared/tsconfig.json'),
  'File should exist - check repository'
);

// Check 7: Package.json files
checkItem(
  'Root package.json',
  () => {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    return pkg.workspaces && pkg.workspaces.includes('frontend');
  },
  'Check package.json workspaces configuration'
);

// Check 8: Vite configuration
checkItem(
  'Vite configuration',
  () => {
    const viteConfig = fs.readFileSync('frontend/vite.config.ts', 'utf8');
    return viteConfig.includes('@aquabell/shared') && viteConfig.includes('resolve');
  },
  'Check frontend/vite.config.ts alias configuration'
);

// Check 9: Local Supabase configuration
checkItem(
  'Frontend configured for local Supabase',
  () => {
    if (!fs.existsSync('frontend/.env')) return false;
    const envContent = fs.readFileSync('frontend/.env', 'utf8');
    return envContent.includes('127.0.0.1:54321') || envContent.includes('localhost:54321');
  },
  'Update frontend/.env to use local Supabase: VITE_SUPABASE_URL=http://127.0.0.1:54321'
);

// Check 10: Local Supabase running
checkItem(
  'Local Supabase status',
  () => {
    try {
      execSync('cd backend && npx supabase status', { stdio: 'pipe' });
      return true;
    } catch {
      return false;
    }
  },
  'Start local Supabase: cd backend && npx supabase start'
);

console.log('\n📋 Summary:');

if (hasErrors) {
  console.log('❌ Some issues found. Please fix the items above and run the health check again.');
  console.log('\n🔧 Quick fix commands:');
  console.log('npm run install:all');
  console.log('cd shared && npm run build && cd ..');
  console.log('cp frontend/.env.example frontend/.env');
  console.log('cp backend/.env.example backend/.env');
  process.exit(1);
} else {
  console.log('✅ All checks passed! Your development environment is ready.');
  console.log('\n🚀 Start development with: npm run dev');
  process.exit(0);
}
