import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import WaterProgressRing from '../WaterProgressRing'

describe('WaterProgressRing', () => {
  const defaultProps = {
    progress: 50,
    currentIntake: 1000,
    dailyGoal: 2000
  }

  it('renders progress ring with correct values', () => {
    render(<WaterProgressRing {...defaultProps} />)
    
    expect(screen.getByText('50%')).toBeInTheDocument()
    expect(screen.getByText('1.0L')).toBeInTheDocument()
  })

  it('shows trophy emoji when goal is reached', () => {
    render(
      <WaterProgressRing 
        progress={100} 
        currentIntake={2000} 
        dailyGoal={2000} 
      />
    )
    
    expect(screen.getByText('🏆')).toBeInTheDocument()
    expect(screen.getByText('100%')).toBeInTheDocument()
  })

  it('shows water drop emoji when goal is not reached', () => {
    render(<WaterProgressRing {...defaultProps} />)
    
    expect(screen.getByText('💧')).toBeInTheDocument()
  })

  it('formats volume correctly for different amounts', () => {
    const { rerender } = render(
      <WaterProgressRing 
        progress={25} 
        currentIntake={500} 
        dailyGoal={2000} 
      />
    )
    
    expect(screen.getByText('500ml')).toBeInTheDocument()
    
    rerender(
      <WaterProgressRing 
        progress={75} 
        currentIntake={1500} 
        dailyGoal={2000} 
      />
    )
    
    expect(screen.getByText('1.5L')).toBeInTheDocument()
  })
})
