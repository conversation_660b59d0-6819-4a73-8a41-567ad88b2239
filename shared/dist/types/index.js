"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnauthorizedError = exports.NotFoundError = exports.ValidationError = exports.AquaBellError = exports.HYDRATION_REMINDERS = exports.DEFAULT_PREFERENCES = exports.DeliveryStatus = exports.NotificationType = void 0;
var NotificationType;
(function (NotificationType) {
    NotificationType["HYDRATION_REMINDER"] = "hydration_reminder";
    NotificationType["DAILY_GOAL_REMINDER"] = "daily_goal_reminder";
    NotificationType["WELCOME"] = "welcome";
    NotificationType["ACHIEVEMENT"] = "achievement";
})(NotificationType || (exports.NotificationType = NotificationType = {}));
var DeliveryStatus;
(function (DeliveryStatus) {
    DeliveryStatus["PENDING"] = "pending";
    DeliveryStatus["SENT"] = "sent";
    DeliveryStatus["DELIVERED"] = "delivered";
    DeliveryStatus["FAILED"] = "failed";
    DeliveryStatus["RETRY"] = "retry";
})(DeliveryStatus || (exports.DeliveryStatus = DeliveryStatus = {}));
// Constants
exports.DEFAULT_PREFERENCES = {
    daily_goal_ml: 2000, // 2 liters
    wake_time: '07:00',
    sleep_time: '22:00',
    reminder_interval_minutes: 60, // 1 hour
    is_notifications_enabled: true
};
exports.HYDRATION_REMINDERS = [
    {
        title: "Time to hydrate! 💧",
        body: "Your body is calling for some refreshing water!",
        emoji: "💧"
    },
    {
        title: "Water break! 🌊",
        body: "Take a moment to drink some water and feel refreshed!",
        emoji: "🌊"
    },
    {
        title: "Hydration station! 🚰",
        body: "Keep your energy up with a nice glass of water!",
        emoji: "🚰"
    },
    {
        title: "Drink up, buttercup! 🌻",
        body: "Your future self will thank you for staying hydrated!",
        emoji: "🌻"
    },
    {
        title: "H2O time! 💦",
        body: "Let's keep those hydration levels topped up!",
        emoji: "💦"
    }
];
// Error Types
class AquaBellError extends Error {
    constructor(message, code, statusCode = 500) {
        super(message);
        this.code = code;
        this.statusCode = statusCode;
        this.name = 'AquaBellError';
    }
}
exports.AquaBellError = AquaBellError;
class ValidationError extends AquaBellError {
    constructor(message, field) {
        super(message, 'VALIDATION_ERROR', 400);
        this.name = 'ValidationError';
    }
}
exports.ValidationError = ValidationError;
class NotFoundError extends AquaBellError {
    constructor(resource) {
        super(`${resource} not found`, 'NOT_FOUND', 404);
        this.name = 'NotFoundError';
    }
}
exports.NotFoundError = NotFoundError;
class UnauthorizedError extends AquaBellError {
    constructor(message = 'Unauthorized') {
        super(message, 'UNAUTHORIZED', 401);
        this.name = 'UnauthorizedError';
    }
}
exports.UnauthorizedError = UnauthorizedError;
