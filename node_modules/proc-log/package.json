{"name": "proc-log", "version": "5.0.0", "files": ["bin/", "lib/"], "main": "lib/index.js", "description": "just emit 'log' events on the process object", "repository": {"type": "git", "url": "git+https://github.com/npm/proc-log.git"}, "author": "GitHub Inc.", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "posttest": "npm run lint", "postsnap": "eslint index.js test/*.js --fix", "lint": "npm run eslint", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "tap": "^16.0.1"}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.3", "publish": true}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}