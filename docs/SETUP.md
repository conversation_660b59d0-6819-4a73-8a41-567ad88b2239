# AquaBell Setup Guide

This guide will help you set up the AquaBell hydration reminder app from scratch.

## Prerequisites

- Node.js 18+ installed
- Supabase account
- Firebase account
- Git

## 1. Clone and Install

```bash
git clone <your-repo-url>
cd aquabell
npm run install:all
```

## 1.5. Build Shared Package (REQUIRED)

⚠️ **IMPORTANT**: You must build the shared package before running frontend or backend:

```bash
cd shared
npm run build
cd ..
```

This step is required because:
- The frontend and backend depend on the shared package
- TypeScript files need to be compiled to JavaScript
- Vite needs the compiled files to resolve imports

**If you skip this step, you'll get errors like:**
```
Failed to resolve entry for package "@aquabell/shared"
```

## 2. Supabase Setup

### Create a Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Wait for the project to be ready
4. Go to Settings > API to get your keys

### Set up Environment Variables

Copy the backend environment file:
```bash
cp backend/.env.example backend/.env
```

Fill in your Supabase credentials:
```env
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Initialize Local Development

```bash
cd backend
npx supabase login
npx supabase init
npx supabase start
```

### Run Migrations

```bash
npm run db:migrate
```

## 3. Firebase Setup

### Create a Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Create a new project
3. Enable Cloud Messaging
4. Generate a web app configuration

### Configure Firebase

1. Go to Project Settings > General
2. Scroll down to "Your apps" and click "Web"
3. Register your app and copy the config
4. Go to Project Settings > Cloud Messaging
5. Generate a new key pair for VAPID

### Set up Environment Variables

Copy the frontend environment file:
```bash
cp frontend/.env.example frontend/.env
```

**For Local Development**: The `.env.example` file is pre-configured with local Supabase settings:
```env
# Local Supabase (automatically configured)
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# Firebase (optional for local development)
VITE_FIREBASE_API_KEY=your_api_key
# ... other Firebase config
```

**For Production**: Replace with your production Supabase and Firebase credentials:
```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key
```

Also update the backend .env for production:
```env
FIREBASE_SERVER_KEY=your_server_key
FIREBASE_PROJECT_ID=your_project_id
```

### Update Service Worker

Edit `frontend/public/firebase-messaging-sw.js` and replace the placeholder config with your actual Firebase config.

## 4. Development

### Start Development Servers

**Prerequisites**: Ensure shared package is built (see step 1.5 above)

```bash
# Terminal 1 - Backend
npm run dev:backend

# Terminal 2 - Frontend
npm run dev:frontend
```

**Alternative**: Start both at once (after building shared package):
```bash
npm run dev
```

### Access the Application

- Frontend: http://localhost:3000
- Supabase Studio: http://localhost:54323

## 5. Testing Notifications

1. Open the app in your browser
2. Allow notification permissions when prompted
3. Go to Settings and ensure notifications are enabled
4. Test the backend function manually:

```bash
curl -X POST http://localhost:54321/functions/v1/send-reminders \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json"
```

## 6. Deployment

### Deploy Backend

```bash
cd backend
npx supabase functions deploy send-reminders
```

### Deploy Frontend

1. Build the frontend:
```bash
cd frontend
npm run build
```

2. Deploy to Vercel:
```bash
npx vercel --prod
```

### Set up Cron Job

In your Supabase dashboard:
1. Go to Edge Functions
2. Find the `send-reminders` function
3. Set up a cron trigger to run every hour

## 7. Production Environment Variables

Make sure to set up environment variables in your production environments:

### Vercel (Frontend)
Add all `VITE_*` variables in your Vercel project settings.

### Supabase (Backend)
Add `FIREBASE_SERVER_KEY` and `FIREBASE_PROJECT_ID` in your Supabase project settings under Edge Functions.

## Troubleshooting

### Common Issues

#### 1. **Shared Package Resolution Errors**
```
Error: Failed to resolve entry for package "@aquabell/shared"
```
**Solution**: Build the shared package first:
```bash
cd shared && npm run build
```

#### 2. **Frontend Won't Start**
```
Error: Cannot resolve module '@aquabell/shared'
```
**Solution**:
1. Build shared package: `cd shared && npm run build`
2. Reinstall frontend dependencies: `cd frontend && npm install`
3. Clear Vite cache: `rm -rf frontend/node_modules/.vite`

#### 3. **TypeScript Errors in Shared Package**
```
Error: Cannot find module '@aquabell/shared' or its corresponding type declarations
```
**Solution**:
1. Ensure shared package is built: `cd shared && npm run build`
2. Check that `shared/dist/index.d.ts` exists
3. Restart TypeScript server in your IDE

#### 4. **Environment Variable Errors**
```
Error: Missing Supabase/Firebase configuration
```
**Solution**:
1. Copy environment files: `cp frontend/.env.example frontend/.env`
2. Fill in your API keys (see sections 2-3 above)
3. Restart development server

#### 5. **Service Worker Registration Errors**
```
Error: Failed to register service worker
```
**Solution**:
1. Ensure you're running on HTTPS or localhost
2. Check that `frontend/public/sw.js` exists
3. Clear browser cache and reload

#### 6. **Notifications not working**
**Solution**:
1. Check that VAPID key is correctly set in Firebase config
2. Ensure Firebase config is valid in both frontend and service worker
3. Test on HTTPS (required for notifications)

#### 7. **Database errors**
**Solution**:
1. Ensure migrations have been run: `npm run db:migrate`
2. Check RLS policies are correct
3. Verify Supabase connection

#### 8. **CORS errors**
**Solution**:
1. Check that your domain is added to Supabase allowed origins
2. Verify API endpoints are correct
3. Ensure environment variables match your Supabase project

#### 9. **Build Errors**
```
Error: Build failed with TypeScript errors
```
**Solution**:
1. Build shared package first: `cd shared && npm run build`
2. Fix any TypeScript errors in the shared package
3. Rebuild: `npm run build`

### Development Workflow

**Recommended development startup sequence:**
```bash
# 1. Install all dependencies
npm run install:all

# 2. Build shared package (REQUIRED)
cd shared && npm run build && cd ..

# 3. Set up environment variables
cp frontend/.env.example frontend/.env
cp backend/.env.example backend/.env
# Fill in your API keys

# 4. Start development servers
npm run dev
```

### Logs and Debugging

- **Frontend**: Browser console (F12 → Console)
- **Backend**: `npm run logs:send-reminders`
- **Supabase**: Dashboard > Logs
- **Shared Package**: Check `shared/dist/` folder exists after build

## Support

If you encounter issues:

1. **First, check**: [TROUBLESHOOTING.md](./TROUBLESHOOTING.md) for common issues and solutions
2. **Verify setup**: All environment variables are set correctly
3. **Check services**: Firebase and Supabase projects are properly configured
4. **Browser support**: Browser supports notifications and service workers
5. **Network**: Check connectivity for API calls

### Quick Diagnostic

Run this health check:
```bash
# Check if everything is set up correctly
ls shared/dist/                    # Should show compiled files
curl http://127.0.0.1:54321/health # Should return OK
curl http://localhost:3000         # Should return HTML
```
