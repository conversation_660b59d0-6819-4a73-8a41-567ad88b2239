# Local Development Backend Configuration

This guide explains how AquaBell is configured to use the local Supabase backend for development.

## 🎯 Overview

AquaBell uses different backend configurations for development and production:

- **Development**: Local Supabase instance (`http://127.0.0.1:54321`)
- **Production**: Remote Supabase project (`https://your-project.supabase.co`)

## 🔧 Current Configuration

### Frontend Environment (`.env`)

```env
# Local Supabase (Development)
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# Firebase (Optional for local development)
VITE_FIREBASE_API_KEY=your_firebase_api_key
# ... other Firebase config
```

### Backend Services

- **API**: `http://127.0.0.1:54321`
- **Database Studio**: `http://127.0.0.1:54323`
- **Edge Functions**: `http://127.0.0.1:54321/functions/v1/`

## 🚀 Starting Local Development

### 1. Start Local Supabase

```bash
cd backend
npx supabase start
```

This starts:
- PostgreSQL database
- Supabase API server
- Edge Functions runtime
- Database Studio UI

### 2. Verify Configuration

```bash
# Check Supabase status
npx supabase status

# Check frontend configuration
grep VITE_SUPABASE_URL frontend/.env
# Should show: http://127.0.0.1:54321
```

### 3. Start Frontend

```bash
npm run dev:frontend
# or
npm run dev  # Starts both frontend and backend
```

## 🔍 Verification

### Automatic Network Tests

The frontend automatically runs network tests in development mode. Check the browser console for:

```
🌐 Network Test Results
✅ Supabase Auth Connection (45ms)
   URL: http://127.0.0.1:54321/auth/v1/session
✅ Database Query (23ms)
   URL: http://127.0.0.1:54321/rest/v1/users
✅ Edge Function (156ms)
   URL: http://127.0.0.1:54321/functions/v1/send-reminders

📊 Summary: 3/3 tests passed
🎉 All network tests passed! Frontend is connected to local backend.
```

### Manual Verification

1. **Browser Network Tab**: 
   - Open DevTools → Network
   - Refresh the page
   - All API calls should go to `127.0.0.1:54321`

2. **Database Studio**:
   - Open http://127.0.0.1:54323
   - View tables and data
   - Changes should reflect in the app

3. **Health Check**:
   ```bash
   npm run health-check
   ```

## 🔄 Switching Between Local and Remote

### For Local Development (Default)

```env
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
```

### For Remote/Production Testing

```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key
```

**Important**: Restart the development server after changing environment variables.

## 🛠️ Local Backend Features

### Database

- **Tables**: All production tables available locally
- **RLS Policies**: Same security rules as production
- **Migrations**: Applied automatically
- **Seed Data**: Can be added for testing

### Edge Functions

- **send-reminders**: Available at `/functions/v1/send-reminders`
- **Local Testing**: Can be called directly via curl or frontend
- **Hot Reload**: Functions update when code changes

### Authentication

- **Anonymous Auth**: Works the same as production
- **Sessions**: Stored locally in browser
- **Policies**: RLS enforced locally

## 🐛 Troubleshooting

### Frontend Still Using Remote Backend

**Symptoms**:
- Network requests go to `*.supabase.co`
- Console shows remote URLs
- Changes in local database don't reflect in app

**Solutions**:
1. Check `.env` file:
   ```bash
   cat frontend/.env | grep SUPABASE_URL
   ```
2. Restart development server:
   ```bash
   # Stop current server (Ctrl+C)
   npm run dev
   ```
3. Clear browser cache and reload

### Local Supabase Not Running

**Symptoms**:
- Connection refused errors
- Network timeouts
- 404 errors on API calls

**Solutions**:
1. Start Supabase:
   ```bash
   cd backend && npx supabase start
   ```
2. Check Docker is running
3. Verify ports are available:
   ```bash
   lsof -i :54321
   ```

### Database Schema Issues

**Symptoms**:
- Table doesn't exist errors
- Permission denied errors
- Missing columns

**Solutions**:
1. Apply migrations:
   ```bash
   cd backend && npx supabase db push
   ```
2. Reset database:
   ```bash
   npx supabase db reset
   ```

## 📊 Performance Benefits

### Local Development Advantages

- **Faster API calls**: No network latency
- **Offline development**: Works without internet
- **Isolated testing**: No impact on production data
- **Instant schema changes**: Migrations apply immediately
- **Free usage**: No API limits or costs

### Development Workflow

1. **Code changes** → Hot reload in browser
2. **Database changes** → Apply migrations locally
3. **Test features** → Instant feedback
4. **Debug issues** → Full access to logs and data
5. **Deploy** → Push to production when ready

## 🔐 Security Notes

### Local vs Production Keys

- **Local anon key**: Safe to commit (demo key)
- **Production keys**: Never commit to repository
- **Service role key**: Only for backend functions

### Data Isolation

- Local database is completely separate from production
- No risk of corrupting production data
- Safe to experiment with schema changes

## 📝 Best Practices

1. **Always develop locally first**
2. **Test with local backend before deploying**
3. **Keep local schema in sync with production**
4. **Use environment variables for all URLs**
5. **Never hardcode backend URLs in components**
6. **Run health checks regularly**
7. **Monitor network tab during development**

This setup ensures a fast, reliable, and isolated development environment while maintaining compatibility with production deployment.
