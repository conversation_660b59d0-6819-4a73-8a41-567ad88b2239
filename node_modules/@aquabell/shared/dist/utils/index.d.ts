/**
 * Check if current time is within user's active hours
 */
export declare function isWithinActiveHours(wakeTime: string, sleepTime: string, timezone?: string, currentTime?: Date): boolean;
/**
 * Calculate next reminder time based on interval and active hours
 */
export declare function getNextReminderTime(lastReminderTime: Date, intervalMinutes: number, wakeTime: string, sleepTime: string, timezone?: string): Date;
/**
 * Format time string to HH:MM format
 */
export declare function formatTime(time: string): string;
/**
 * Validate time string format (HH:MM)
 */
export declare function isValidTimeFormat(time: string): boolean;
/**
 * Convert milliliters to user-friendly format
 */
export declare function formatVolume(ml: number): string;
/**
 * Generate a random hydration reminder from predefined list
 */
export declare function getRandomReminder(): {
    title: string;
    body: string;
    emoji: string;
};
/**
 * Validate email format
 */
export declare function isValidEmail(email: string): boolean;
/**
 * Sanitize user input
 */
export declare function sanitizeString(input: string): string;
/**
 * Calculate progress percentage
 */
export declare function calculateProgress(current: number, goal: number): number;
/**
 * Debounce function for API calls
 */
export declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
/**
 * Sleep utility for async operations
 */
export declare function sleep(ms: number): Promise<void>;
/**
 * Generate UUID v4
 */
export declare function generateUUID(): string;
