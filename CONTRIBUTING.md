# Contributing to A<PERSON>Bell

Thank you for your interest in contributing to AquaBell! This document provides guidelines and information for contributors.

## Getting Started

### Prerequisites
- Node.js 18+
- Git
- A Supabase account (for backend development)
- A Firebase account (for notification testing)

### Development Setup

1. **Fork and Clone**
```bash
git clone https://github.com/your-username/aquabell.git
cd aquabell
```

2. **Install Dependencies**
```bash
npm run install:all
```

3. **Set Up Environment**
```bash
# Copy environment files
cp frontend/.env.example frontend/.env
cp backend/.env.example backend/.env

# Fill in your API keys and configuration
```

4. **Start Development**
```bash
# Start all services
npm run dev

# Or start individually
npm run dev:frontend  # Frontend only
npm run dev:backend   # Backend only
```

## Project Structure

```
aquabell/
├── frontend/          # React TypeScript app
├── backend/           # Supabase Edge Functions
├── shared/            # Common types and utilities
├── docs/              # Documentation
└── .github/           # GitHub workflows
```

## Development Guidelines

### Code Style

- **TypeScript**: Use strict type checking
- **ESLint**: Follow the configured rules
- **Prettier**: Code formatting (if configured)
- **Naming**: Use descriptive, camelCase names
- **Comments**: Document complex logic and public APIs

### Git Workflow

1. **Create a Branch**
```bash
git checkout -b feature/your-feature-name
# or
git checkout -b fix/issue-description
```

2. **Make Changes**
- Write clear, focused commits
- Test your changes thoroughly
- Update documentation if needed

3. **Commit Messages**
```bash
# Good commit messages
git commit -m "feat: add custom water amount modal"
git commit -m "fix: notification permission handling on iOS"
git commit -m "docs: update setup instructions"

# Use conventional commits format
# feat: new feature
# fix: bug fix
# docs: documentation
# style: formatting
# refactor: code restructuring
# test: adding tests
# chore: maintenance
```

4. **Push and Create PR**
```bash
git push origin your-branch-name
# Then create a Pull Request on GitHub
```

### Testing

#### Running Tests
```bash
# Run all tests
npm test

# Run specific package tests
cd shared && npm test
cd frontend && npm test

# Run with coverage
npm run test:coverage
```

#### Writing Tests
- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test component interactions
- **E2E Tests**: Test complete user workflows (future)

Example test structure:
```typescript
import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import MyComponent from '../MyComponent'

describe('MyComponent', () => {
  it('should render correctly', () => {
    render(<MyComponent />)
    expect(screen.getByText('Expected Text')).toBeInTheDocument()
  })
})
```

### Code Review Process

1. **Self Review**
   - Test your changes thoroughly
   - Check for TypeScript errors
   - Run linting and formatting
   - Update documentation

2. **PR Requirements**
   - Clear description of changes
   - Link to related issues
   - Screenshots for UI changes
   - Test coverage for new features

3. **Review Criteria**
   - Code quality and style
   - Performance impact
   - Security considerations
   - Accessibility compliance
   - Mobile responsiveness

## Types of Contributions

### 🐛 Bug Reports
- Use the bug report template
- Include steps to reproduce
- Provide browser/device information
- Add screenshots if applicable

### ✨ Feature Requests
- Use the feature request template
- Explain the use case
- Consider implementation complexity
- Discuss with maintainers first

### 📝 Documentation
- Fix typos and grammar
- Improve clarity and examples
- Add missing documentation
- Update outdated information

### 🎨 UI/UX Improvements
- Follow the design system
- Maintain accessibility standards
- Test on multiple devices
- Consider user experience impact

### 🔧 Code Contributions
- Follow coding standards
- Add appropriate tests
- Update documentation
- Consider backward compatibility

## Specific Areas

### Frontend Development
- **React Components**: Functional components with hooks
- **State Management**: Zustand for global state
- **Styling**: Tailwind CSS utility classes
- **Routing**: React Router for navigation
- **Testing**: Vitest and React Testing Library

### Backend Development
- **Edge Functions**: Deno runtime with TypeScript
- **Database**: PostgreSQL with Supabase
- **Authentication**: Supabase Auth
- **Notifications**: Firebase Cloud Messaging
- **Cron Jobs**: Scheduled functions

### Shared Package
- **Types**: TypeScript interfaces and types
- **Utilities**: Pure functions for common operations
- **Constants**: Shared configuration and defaults
- **Testing**: Jest for unit tests

## Design Guidelines

### UI Principles
- **Minimal**: Clean, uncluttered interface
- **Friendly**: Warm, approachable design
- **Cartoony**: Playful use of emojis and illustrations
- **Mobile-First**: Optimized for smartphone usage
- **Accessible**: WCAG 2.1 AA compliance

### Color Palette
- **Primary**: Blue tones (#3B82F6)
- **Water**: Cyan/aqua tones (#0EA5E9)
- **Success**: Green tones (#22C55E)
- **Warning**: Yellow/orange tones
- **Error**: Red tones

### Typography
- **Headers**: Poppins font family
- **Body**: Inter font family
- **Sizes**: Responsive scale (text-sm to text-4xl)

## Performance Guidelines

### Frontend Performance
- **Bundle Size**: Keep under 500KB gzipped
- **Loading**: First paint under 1.5s
- **Images**: Use WebP format, optimize sizes
- **Caching**: Implement appropriate cache strategies

### Backend Performance
- **Database**: Use indexes for queries
- **Functions**: Optimize cold start times
- **Notifications**: Batch operations when possible
- **Monitoring**: Track performance metrics

## Security Guidelines

### Frontend Security
- **Environment Variables**: Never expose secrets
- **Input Validation**: Sanitize user inputs
- **XSS Prevention**: Use React's built-in protections
- **HTTPS**: Enforce secure connections

### Backend Security
- **Authentication**: Validate all requests
- **Authorization**: Use Row Level Security
- **Input Validation**: Validate all inputs
- **Rate Limiting**: Prevent abuse

## Accessibility Guidelines

### Requirements
- **Keyboard Navigation**: All features accessible via keyboard
- **Screen Readers**: Semantic HTML and ARIA labels
- **Color Contrast**: Minimum 4.5:1 ratio
- **Focus Management**: Clear focus indicators
- **Alternative Text**: Descriptive alt text for images

### Testing
- **Automated**: Use axe-core for testing
- **Manual**: Test with screen readers
- **Keyboard**: Navigate without mouse
- **Color**: Test with color blindness simulators

## Release Process

### Version Numbering
- **Major**: Breaking changes (1.0.0 → 2.0.0)
- **Minor**: New features (1.0.0 → 1.1.0)
- **Patch**: Bug fixes (1.0.0 → 1.0.1)

### Release Steps
1. Update version numbers
2. Update CHANGELOG.md
3. Create release branch
4. Run full test suite
5. Deploy to staging
6. Create GitHub release
7. Deploy to production

## Getting Help

### Communication Channels
- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Pull Requests**: Code review and collaboration

### Resources
- [Setup Guide](./docs/SETUP.md)
- [API Documentation](./docs/API.md)
- [Deployment Guide](./docs/DEPLOYMENT.md)
- [Feature Overview](./docs/FEATURES.md)

## Recognition

Contributors will be recognized in:
- README.md contributors section
- GitHub contributors page
- Release notes for significant contributions

Thank you for contributing to AquaBell! 💧
