import { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { useAuthStore } from './stores/authStore'
import { useNotificationStore } from './stores/notificationStore'
import { initializeFirebase } from './services/firebase'
import { supabase } from './services/supabase'
import { runNetworkTestsInDev } from './utils/networkTest'
import { runErrorFixTestsInDev } from './utils/errorTestUtils'

// Components
import Layout from './components/Layout'
import HomePage from './pages/HomePage'
import OnboardingPage from './pages/OnboardingPage'
import SettingsPage from './pages/SettingsPage'
import AchievementsPage from './pages/AchievementsPage'
import LoadingSpinner from './components/LoadingSpinner'
import NotificationBanner from './components/NotificationBanner'

function App() {
  const { user, loading, initialize } = useAuthStore()
  const { initializeNotifications } = useNotificationStore()

  useEffect(() => {
    // Initialize Firebase
    initializeFirebase()

    // Initialize auth
    initialize()

    // Run network tests in development
    runNetworkTestsInDev()

    // Run error fix verification tests in development
    runErrorFixTestsInDev()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          // Initialize notifications after sign in
          await initializeNotifications()
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [initialize, initializeNotifications])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    )
  }

  return (
    <Router>
      <div className="min-h-screen">
        <NotificationBanner />
        
        <Routes>
          <Route path="/" element={<Layout />}>
            {!user ? (
              <Route index element={<OnboardingPage />} />
            ) : (
              <>
                <Route index element={<HomePage />} />
                <Route path="/achievements" element={<AchievementsPage />} />
                <Route path="/settings" element={<SettingsPage />} />
              </>
            )}
          </Route>
        </Routes>
      </div>
    </Router>
  )
}

export default App
