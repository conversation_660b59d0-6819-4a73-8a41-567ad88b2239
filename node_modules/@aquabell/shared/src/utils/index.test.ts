import {
  isWithinActiveHours,
  getNextReminderTime,
  formatTime,
  isValidTimeFormat,
  formatVolume,
  getRandomReminder,
  isValidEmail,
  sanitizeString,
  calculateProgress,
  generateUUID
} from './index'

describe('isWithinActiveHours', () => {
  test('should return true when current time is within active hours', () => {
    const mockTime = new Date('2024-01-01T10:00:00Z')
    expect(isWithinActiveHours('07:00', '22:00', 'UTC', mockTime)).toBe(true)
  })

  test('should return false when current time is outside active hours', () => {
    const mockTime = new Date('2024-01-01T02:00:00Z')
    expect(isWithinActiveHours('07:00', '22:00', 'UTC', mockTime)).toBe(false)
  })

  test('should handle overnight active hours', () => {
    const mockTime = new Date('2024-01-01T01:00:00Z')
    expect(isWithinActiveHours('22:00', '06:00', 'UTC', mockTime)).toBe(true)
  })
})

describe('formatTime', () => {
  test('should format time correctly', () => {
    expect(formatTime('7:5')).toBe('07:05')
    expect(formatTime('12:30')).toBe('12:30')
    expect(formatTime('0:0')).toBe('00:00')
  })
})

describe('isValidTimeFormat', () => {
  test('should validate correct time formats', () => {
    expect(isValidTimeFormat('07:30')).toBe(true)
    expect(isValidTimeFormat('23:59')).toBe(true)
    expect(isValidTimeFormat('00:00')).toBe(true)
  })

  test('should reject invalid time formats', () => {
    expect(isValidTimeFormat('25:00')).toBe(false)
    expect(isValidTimeFormat('12:60')).toBe(false)
    expect(isValidTimeFormat('abc')).toBe(false)
    expect(isValidTimeFormat('12')).toBe(false)
  })
})

describe('formatVolume', () => {
  test('should format milliliters correctly', () => {
    expect(formatVolume(500)).toBe('500ml')
    expect(formatVolume(250)).toBe('250ml')
  })

  test('should format liters correctly', () => {
    expect(formatVolume(1000)).toBe('1.0L')
    expect(formatVolume(1500)).toBe('1.5L')
    expect(formatVolume(2000)).toBe('2.0L')
  })
})

describe('getRandomReminder', () => {
  test('should return a valid reminder object', () => {
    const reminder = getRandomReminder()
    expect(reminder).toHaveProperty('title')
    expect(reminder).toHaveProperty('body')
    expect(reminder).toHaveProperty('emoji')
    expect(typeof reminder.title).toBe('string')
    expect(typeof reminder.body).toBe('string')
    expect(typeof reminder.emoji).toBe('string')
  })

  test('should return different reminders on multiple calls', () => {
    const reminders = Array.from({ length: 10 }, () => getRandomReminder())
    const uniqueTitles = new Set(reminders.map(r => r.title))
    // Should have some variety (not all the same)
    expect(uniqueTitles.size).toBeGreaterThan(1)
  })
})

describe('isValidEmail', () => {
  test('should validate correct email formats', () => {
    expect(isValidEmail('<EMAIL>')).toBe(true)
    expect(isValidEmail('<EMAIL>')).toBe(true)
    expect(isValidEmail('<EMAIL>')).toBe(true)
  })

  test('should reject invalid email formats', () => {
    expect(isValidEmail('invalid')).toBe(false)
    expect(isValidEmail('test@')).toBe(false)
    expect(isValidEmail('@example.com')).toBe(false)
    expect(isValidEmail('test.example.com')).toBe(false)
  })
})

describe('sanitizeString', () => {
  test('should remove dangerous characters', () => {
    expect(sanitizeString('<script>alert("xss")</script>')).toBe('scriptalert("xss")/script')
    expect(sanitizeString('Hello > World')).toBe('Hello  World')
  })

  test('should trim whitespace', () => {
    expect(sanitizeString('  hello world  ')).toBe('hello world')
  })
})

describe('calculateProgress', () => {
  test('should calculate progress correctly', () => {
    expect(calculateProgress(500, 1000)).toBe(50)
    expect(calculateProgress(750, 1000)).toBe(75)
    expect(calculateProgress(1000, 1000)).toBe(100)
  })

  test('should cap progress at 100%', () => {
    expect(calculateProgress(1500, 1000)).toBe(100)
  })

  test('should handle zero goal', () => {
    expect(calculateProgress(500, 0)).toBe(0)
  })
})

describe('generateUUID', () => {
  test('should generate valid UUID format', () => {
    const uuid = generateUUID()
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    expect(uuid).toMatch(uuidRegex)
  })

  test('should generate unique UUIDs', () => {
    const uuids = Array.from({ length: 100 }, () => generateUUID())
    const uniqueUuids = new Set(uuids)
    expect(uniqueUuids.size).toBe(100)
  })
})
