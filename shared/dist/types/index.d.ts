export interface User {
    id: string;
    created_at: string;
    updated_at: string;
    is_active: boolean;
    fcm_token?: string;
    timezone: string;
}
export interface UserPreferences {
    id: string;
    user_id: string;
    daily_goal_ml: number;
    wake_time: string;
    sleep_time: string;
    reminder_interval_minutes: number;
    is_notifications_enabled: boolean;
    created_at: string;
    updated_at: string;
}
export interface NotificationLog {
    id: string;
    user_id: string;
    notification_type: NotificationType;
    title: string;
    body: string;
    sent_at: string;
    delivery_status: DeliveryStatus;
    fcm_message_id?: string;
    error_message?: string;
    retry_count: number;
}
export declare enum NotificationType {
    HYDRATION_REMINDER = "hydration_reminder",
    DAILY_GOAL_REMINDER = "daily_goal_reminder",
    WELCOME = "welcome",
    ACHIEVEMENT = "achievement"
}
export declare enum DeliveryStatus {
    PENDING = "pending",
    SENT = "sent",
    DELIVERED = "delivered",
    FAILED = "failed",
    RETRY = "retry"
}
export interface FCMMessage {
    token: string;
    notification: {
        title: string;
        body: string;
        icon?: string;
        badge?: string;
    };
    data?: Record<string, string>;
    webpush?: {
        headers?: Record<string, string>;
        data?: Record<string, string>;
        notification?: {
            title?: string;
            body?: string;
            icon?: string;
            badge?: string;
            image?: string;
            tag?: string;
            requireInteraction?: boolean;
            silent?: boolean;
            timestamp?: number;
            actions?: Array<{
                action: string;
                title: string;
                icon?: string;
            }>;
        };
    };
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}
export interface TimeRange {
    start: string;
    end: string;
}
export interface HydrationReminder {
    title: string;
    body: string;
    emoji: string;
}
export declare const DEFAULT_PREFERENCES: Omit<UserPreferences, 'id' | 'user_id' | 'created_at' | 'updated_at'>;
export declare const HYDRATION_REMINDERS: HydrationReminder[];
export declare class AquaBellError extends Error {
    code: string;
    statusCode: number;
    constructor(message: string, code: string, statusCode?: number);
}
export declare class ValidationError extends AquaBellError {
    constructor(message: string, field?: string);
}
export declare class NotFoundError extends AquaBellError {
    constructor(resource: string);
}
export declare class UnauthorizedError extends AquaBellError {
    constructor(message?: string);
}
