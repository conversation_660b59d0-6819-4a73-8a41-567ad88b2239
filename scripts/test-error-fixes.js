#!/usr/bin/env node

/**
 * Test script to verify the three main console errors are fixed
 * Run this script to check if the fixes are working correctly
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Testing Error Fixes...\n');

// Test 1: Check environment configuration
console.log('1. Checking environment configuration...');

const frontendEnvPath = path.join(__dirname, '../frontend/.env');
const backendEnvPath = path.join(__dirname, '../backend/.env');

try {
  const frontendEnv = fs.readFileSync(frontendEnvPath, 'utf8');
  const backendEnv = fs.readFileSync(backendEnvPath, 'utf8');
  
  const frontendUrl = frontendEnv.match(/VITE_SUPABASE_URL=(.+)/)?.[1];
  const backendUrl = backendEnv.match(/SUPABASE_URL=(.+)/)?.[1];
  
  if (frontendUrl === backendUrl) {
    console.log('   ✅ Frontend and backend URLs match:', frontendUrl);
  } else {
    console.log('   ❌ URL mismatch:');
    console.log('      Frontend:', frontendUrl);
    console.log('      Backend:', backendUrl);
  }
  
  const isLocal = frontendUrl?.includes('127.0.0.1') || frontendUrl?.includes('localhost');
  console.log('   📍 Environment:', isLocal ? 'Local Development' : 'Production');
  
} catch (error) {
  console.log('   ❌ Error reading environment files:', error.message);
}

// Test 2: Check Supabase status
console.log('\n2. Checking Supabase status...');

try {
  const supabaseStatus = execSync('cd backend && npx supabase status', { 
    encoding: 'utf8',
    timeout: 10000 
  });
  
  if (supabaseStatus.includes('supabase local development setup is running')) {
    console.log('   ✅ Supabase local instance is running');
    
    // Extract API URL
    const apiUrlMatch = supabaseStatus.match(/API URL: (.+)/);
    if (apiUrlMatch) {
      console.log('   📍 API URL:', apiUrlMatch[1]);
    }
  } else {
    console.log('   ❌ Supabase local instance not running');
  }
} catch (error) {
  console.log('   ❌ Error checking Supabase status:', error.message);
  console.log('   💡 Try running: cd backend && npx supabase start');
}

// Test 3: Check if frontend is running
console.log('\n3. Checking frontend status...');

try {
  const response = execSync('curl -s -o /dev/null -w "%{http_code}" http://localhost:3000', { 
    encoding: 'utf8',
    timeout: 5000 
  });
  
  if (response.trim() === '200') {
    console.log('   ✅ Frontend is running at http://localhost:3000');
  } else {
    console.log('   ❌ Frontend not responding (HTTP', response.trim() + ')');
  }
} catch (error) {
  console.log('   ❌ Frontend not running');
  console.log('   💡 Try running: cd frontend && npm run dev');
}

// Test 4: Check for error fix files
console.log('\n4. Checking error fix implementation...');

const errorTestUtilsPath = path.join(__dirname, '../frontend/src/utils/errorTestUtils.ts');
const summaryPath = path.join(__dirname, '../docs/ERROR_FIXES_SUMMARY.md');

if (fs.existsSync(errorTestUtilsPath)) {
  console.log('   ✅ Error test utilities implemented');
} else {
  console.log('   ❌ Error test utilities missing');
}

if (fs.existsSync(summaryPath)) {
  console.log('   ✅ Error fixes documentation available');
} else {
  console.log('   ❌ Error fixes documentation missing');
}

// Summary
console.log('\n📊 Summary:');
console.log('   The three main console errors should now be fixed:');
console.log('   1. Anonymous sign-ins error - Fixed by environment alignment');
console.log('   2. Supabase connection null error - Fixed by improved error handling');
console.log('   3. Edge function error - Fixed by better error handling and environment detection');
console.log('\n💡 Next steps:');
console.log('   1. Open http://localhost:3000 in your browser');
console.log('   2. Check the browser console for verification test results');
console.log('   3. Look for green checkmarks and "All error fixes verified!" message');
console.log('   4. If you see any red errors, check the troubleshooting section in ERROR_FIXES_SUMMARY.md');

console.log('\n🎉 Error fix verification complete!');
