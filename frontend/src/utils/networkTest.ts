// Network testing utilities for development
import { supabase } from '../services/supabase'

export interface NetworkTestResult {
  test: string
  success: boolean
  url?: string
  error?: string
  responseTime?: number
}

export async function testLocalBackendConnection(): Promise<NetworkTestResult[]> {
  const results: NetworkTestResult[] = []
  
  // Test 1: Basic Supabase connection
  try {
    const startTime = Date.now()
    const { data, error } = await supabase.auth.getSession()
    const responseTime = Date.now() - startTime
    
    results.push({
      test: 'Supabase Auth Connection',
      success: !error,
      url: `${import.meta.env.VITE_SUPABASE_URL}/auth/v1/session`,
      error: error?.message,
      responseTime
    })
  } catch (error) {
    results.push({
      test: 'Supabase Auth Connection',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 2: Database query
  try {
    const startTime = Date.now()
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1)
    const responseTime = Date.now() - startTime
    
    results.push({
      test: 'Database Query',
      success: !error,
      url: `${import.meta.env.VITE_SUPABASE_URL}/rest/v1/users`,
      error: error?.message,
      responseTime
    })
  } catch (error) {
    results.push({
      test: 'Database Query',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 3: Edge Function (if available)
  try {
    const startTime = Date.now()
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/send-reminders`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json'
      }
    })
    const responseTime = Date.now() - startTime
    
    results.push({
      test: 'Edge Function',
      success: response.status < 500, // Accept 4xx errors as "working"
      url: `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/send-reminders`,
      error: response.status >= 500 ? `HTTP ${response.status}` : undefined,
      responseTime
    })
  } catch (error) {
    results.push({
      test: 'Edge Function',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  return results
}

export function logNetworkTestResults(results: NetworkTestResult[]) {
  console.group('🌐 Network Test Results')
  
  results.forEach(result => {
    const icon = result.success ? '✅' : '❌'
    const timeStr = result.responseTime ? ` (${result.responseTime}ms)` : ''
    
    console.log(`${icon} ${result.test}${timeStr}`)
    
    if (result.url) {
      console.log(`   URL: ${result.url}`)
    }
    
    if (result.error) {
      console.log(`   Error: ${result.error}`)
    }
  })
  
  const successCount = results.filter(r => r.success).length
  const totalCount = results.length
  
  console.log(`\n📊 Summary: ${successCount}/${totalCount} tests passed`)
  
  if (successCount === totalCount) {
    console.log('🎉 All network tests passed! Frontend is connected to local backend.')
  } else {
    console.warn('⚠️ Some network tests failed. Check configuration.')
  }
  
  console.groupEnd()
}

// Auto-run network tests in development mode
export function runNetworkTestsInDev() {
  if (import.meta.env.DEV) {
    console.log('🔧 Development mode detected - running network tests...')
    
    // Run tests after a short delay to allow app initialization
    setTimeout(async () => {
      try {
        const results = await testLocalBackendConnection()
        logNetworkTestResults(results)
      } catch (error) {
        console.error('❌ Network tests failed:', error)
      }
    }, 2000)
  }
}
