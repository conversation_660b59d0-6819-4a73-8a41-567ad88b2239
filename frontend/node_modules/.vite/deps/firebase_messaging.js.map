{"version": 3, "sources": ["../../../../node_modules/@firebase/installations/src/util/constants.ts", "../../../../node_modules/@firebase/installations/src/util/errors.ts", "../../../../node_modules/@firebase/installations/src/functions/common.ts", "../../../../node_modules/@firebase/installations/src/functions/create-installation-request.ts", "../../../../node_modules/@firebase/installations/src/util/sleep.ts", "../../../../node_modules/@firebase/installations/src/helpers/buffer-to-base64-url-safe.ts", "../../../../node_modules/@firebase/installations/src/helpers/generate-fid.ts", "../../../../node_modules/@firebase/installations/src/util/get-key.ts", "../../../../node_modules/@firebase/installations/src/helpers/fid-changed.ts", "../../../../node_modules/@firebase/installations/src/helpers/idb-manager.ts", "../../../../node_modules/@firebase/installations/src/helpers/get-installation-entry.ts", "../../../../node_modules/@firebase/installations/src/functions/generate-auth-token-request.ts", "../../../../node_modules/@firebase/installations/src/helpers/refresh-auth-token.ts", "../../../../node_modules/@firebase/installations/src/api/get-id.ts", "../../../../node_modules/@firebase/installations/src/api/get-token.ts", "../../../../node_modules/@firebase/installations/src/functions/delete-installation-request.ts", "../../../../node_modules/@firebase/installations/src/api/delete-installations.ts", "../../../../node_modules/@firebase/installations/src/api/on-id-change.ts", "../../../../node_modules/@firebase/installations/src/api/get-installations.ts", "../../../../node_modules/@firebase/installations/src/helpers/extract-app-config.ts", "../../../../node_modules/@firebase/installations/src/functions/config.ts", "../../../../node_modules/@firebase/installations/src/index.ts", "../../../../node_modules/@firebase/messaging/src/util/constants.ts", "../../../../node_modules/@firebase/messaging/src/interfaces/internal-message-payload.ts", "../../../../node_modules/@firebase/messaging/src/helpers/array-base64-translator.ts", "../../../../node_modules/@firebase/messaging/src/helpers/migrate-old-database.ts", "../../../../node_modules/@firebase/messaging/src/internals/idb-manager.ts", "../../../../node_modules/@firebase/messaging/src/util/errors.ts", "../../../../node_modules/@firebase/messaging/src/internals/requests.ts", "../../../../node_modules/@firebase/messaging/src/internals/token-manager.ts", "../../../../node_modules/@firebase/messaging/src/helpers/externalizePayload.ts", "../../../../node_modules/@firebase/messaging/src/helpers/is-console-message.ts", "../../../../node_modules/@firebase/messaging/src/helpers/logToFirelog.ts", "../../../../node_modules/@firebase/messaging/src/helpers/extract-app-config.ts", "../../../../node_modules/@firebase/messaging/src/messaging-service.ts", "../../../../node_modules/@firebase/messaging/src/helpers/registerDefaultSw.ts", "../../../../node_modules/@firebase/messaging/src/helpers/updateSwReg.ts", "../../../../node_modules/@firebase/messaging/src/helpers/updateVapidKey.ts", "../../../../node_modules/@firebase/messaging/src/api/getToken.ts", "../../../../node_modules/@firebase/messaging/src/helpers/logToScion.ts", "../../../../node_modules/@firebase/messaging/src/listeners/window-listener.ts", "../../../../node_modules/@firebase/messaging/src/helpers/register.ts", "../../../../node_modules/@firebase/messaging/src/api/isSupported.ts", "../../../../node_modules/@firebase/messaging/src/api/deleteToken.ts", "../../../../node_modules/@firebase/messaging/src/api/onMessage.ts", "../../../../node_modules/@firebase/messaging/src/api.ts", "../../../../node_modules/@firebase/messaging/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../../package.json';\n\nexport const PENDING_TIMEOUT_MS = 10000;\n\nexport const PACKAGE_VERSION = `w:${version}`;\nexport const INTERNAL_AUTH_VERSION = 'FIS_v2';\n\nexport const INSTALLATIONS_API_URL =\n  'https://firebaseinstallations.googleapis.com/v1';\n\nexport const TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\n\nexport const SERVICE = 'installations';\nexport const SERVICE_NAME = 'Installations';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from './constants';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  NOT_REGISTERED = 'not-registered',\n  INSTALLATION_NOT_FOUND = 'installation-not-found',\n  REQUEST_FAILED = 'request-failed',\n  APP_OFFLINE = 'app-offline',\n  DELETE_PENDING_REGISTRATION = 'delete-pending-registration'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.NOT_REGISTERED]: 'Firebase Installation is not registered.',\n  [ErrorCode.INSTALLATION_NOT_FOUND]: 'Firebase Installation not found.',\n  [ErrorCode.REQUEST_FAILED]:\n    '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [ErrorCode.APP_OFFLINE]: 'Could not process request. Application offline.',\n  [ErrorCode.DELETE_PENDING_REGISTRATION]:\n    \"Can't delete installation while there is a pending registration request.\"\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.REQUEST_FAILED]: {\n    requestName: string;\n    [index: string]: string | number; // to make TypeScript 3.8 happy\n  } & ServerErrorData;\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n\nexport interface ServerErrorData {\n  serverCode: number;\n  serverMessage: string;\n  serverStatus: string;\n}\n\nexport type ServerError = FirebaseError & { customData: ServerErrorData };\n\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nexport function isServerError(error: unknown): error is ServerError {\n  return (\n    error instanceof FirebaseError &&\n    error.code.includes(ErrorCode.REQUEST_FAILED)\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport {\n  INSTALLATIONS_API_URL,\n  INTERNAL_AUTH_VERSION\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { AppConfig } from '../interfaces/installation-impl';\n\nexport function getInstallationsEndpoint({ projectId }: AppConfig): string {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\n\nexport function extractAuthTokenInfoFromResponse(\n  response: GenerateAuthTokenResponse\n): CompletedAuthToken {\n  return {\n    token: response.token,\n    requestStatus: RequestStatus.COMPLETED,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\n\nexport async function getErrorFromResponse(\n  requestName: string,\n  response: Response\n): Promise<FirebaseError> {\n  const responseJson: ErrorResponse = await response.json();\n  const errorData = responseJson.error;\n  return ERROR_FACTORY.create(ErrorCode.REQUEST_FAILED, {\n    requestName,\n    serverCode: errorData.code,\n    serverMessage: errorData.message,\n    serverStatus: errorData.status\n  });\n}\n\nexport function getHeaders({ apiKey }: AppConfig): Headers {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\nexport function getHeadersWithAuth(\n  appConfig: AppConfig,\n  { refreshToken }: RegisteredInstallationEntry\n): Headers {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n\nexport interface ErrorResponse {\n  error: {\n    code: number;\n    message: string;\n    status: string;\n  };\n}\n\n/**\n * Calls the passed in fetch wrapper and returns the response.\n * If the returned response has a status of 5xx, re-runs the function once and\n * returns the response.\n */\nexport async function retryIfServerError(\n  fn: () => Promise<Response>\n): Promise<Response> {\n  const result = await fn();\n\n  if (result.status >= 500 && result.status < 600) {\n    // Internal Server Error. Retry request.\n    return fn();\n  }\n\n  return result;\n}\n\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn: string): number {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\n\nfunction getAuthorizationHeader(refreshToken: string): string {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CreateInstallationResponse } from '../interfaces/api-response';\nimport {\n  InProgressInstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { INTERNAL_AUTH_VERSION, PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeaders,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\n\nexport async function createInstallationRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  { fid }: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  const endpoint = getInstallationsEndpoint(appConfig);\n\n  const headers = getHeaders(appConfig);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    fid,\n    authVersion: INTERNAL_AUTH_VERSION,\n    appId: appConfig.appId,\n    sdkVersion: PACKAGE_VERSION\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: CreateInstallationResponse = await response.json();\n    const registeredInstallationEntry: RegisteredInstallationEntry = {\n      fid: responseValue.fid || fid,\n      registrationStatus: RequestStatus.COMPLETED,\n      refreshToken: responseValue.refreshToken,\n      authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n    };\n    return registeredInstallationEntry;\n  } else {\n    throw await getErrorFromResponse('Create Installation', response);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function bufferToBase64UrlSafe(array: Uint8Array): string {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bufferToBase64UrlSafe } from './buffer-to-base64-url-safe';\n\nexport const VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nexport const INVALID_FID = '';\n\n/**\n * Generates a new FID using random values from Web Crypto API.\n * Returns an empty string if FID generation fails for any reason.\n */\nexport function generateFid(): string {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto =\n      self.crypto || (self as unknown as { msCrypto: Crypto }).msCrypto;\n    crypto.getRandomValues(fidByteArray);\n\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\n\n    const fid = encode(fidByteArray);\n\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray: Uint8Array): string {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\n\n/** Returns a string key that can be used to identify the app. */\nexport function getKey(appConfig: AppConfig): string {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getKey } from '../util/get-key';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { IdChangeCallbackFn } from '../api';\n\nconst fidChangeCallbacks: Map<string, Set<IdChangeCallbackFn>> = new Map();\n\n/**\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\n * change to other tabs.\n */\nexport function fidChanged(appConfig: AppConfig, fid: string): void {\n  const key = getKey(appConfig);\n\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\n\nexport function addCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n\n  const key = getKey(appConfig);\n\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\n\nexport function removeCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  const key = getKey(appConfig);\n\n  const callbackSet = fidChangeCallbacks.get(key);\n\n  if (!callbackSet) {\n    return;\n  }\n\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\n\nfunction callFidChangeCallbacks(key: string, fid: string): void {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\n\nfunction broadcastFidChange(key: string, fid: string): void {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({ key, fid });\n  }\n  closeBroadcastChannel();\n}\n\nlet broadcastChannel: BroadcastChannel | null = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel(): BroadcastChannel | null {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\n\nfunction closeBroadcastChannel(): void {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, openDB } from 'idb';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { InstallationEntry } from '../interfaces/installation-entry';\nimport { getKey } from '../util/get-key';\nimport { fidChanged } from './fid-changed';\n\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\n\ninterface InstallationsDB extends DBSchema {\n  'firebase-installations-store': {\n    key: string;\n    value: InstallationEntry | undefined;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<InstallationsDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<InstallationsDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function get(\n  appConfig: AppConfig\n): Promise<InstallationEntry | undefined> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  return db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key) as Promise<InstallationEntry>;\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function set<ValueType extends InstallationEntry>(\n  appConfig: AppConfig,\n  value: ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = (await objectStore.get(key)) as InstallationEntry;\n  await objectStore.put(value, key);\n  await tx.done;\n\n  if (!oldValue || oldValue.fid !== value.fid) {\n    fidChanged(appConfig, value.fid);\n  }\n\n  return value;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function remove(appConfig: AppConfig): Promise<void> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/**\n * Atomically updates a record with the result of updateFn, which gets\n * called with the current value. If newValue is undefined, the record is\n * deleted instead.\n * @return Updated value\n */\nexport async function update<ValueType extends InstallationEntry | undefined>(\n  appConfig: AppConfig,\n  updateFn: (previousValue: InstallationEntry | undefined) => ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const store = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue: InstallationEntry | undefined = (await store.get(\n    key\n  )) as InstallationEntry;\n  const newValue = updateFn(oldValue);\n\n  if (newValue === undefined) {\n    await store.delete(key);\n  } else {\n    await store.put(newValue, key);\n  }\n  await tx.done;\n\n  if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n    fidChanged(appConfig, newValue.fid);\n  }\n\n  return newValue;\n}\n\nexport async function clear(): Promise<void> {\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).clear();\n  await tx.done;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createInstallationRequest } from '../functions/create-installation-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  InProgressInstallationEntry,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { generateFid, INVALID_FID } from './generate-fid';\nimport { remove, set, update } from './idb-manager';\n\nexport interface InstallationEntryWithRegistrationPromise {\n  installationEntry: InstallationEntry;\n  /** Exist iff the installationEntry is not registered. */\n  registrationPromise?: Promise<RegisteredInstallationEntry>;\n}\n\n/**\n * Updates and returns the InstallationEntry from the database.\n * Also triggers a registration request if it is necessary and possible.\n */\nexport async function getInstallationEntry(\n  installations: FirebaseInstallationsImpl\n): Promise<InstallationEntryWithRegistrationPromise> {\n  let registrationPromise: Promise<RegisteredInstallationEntry> | undefined;\n\n  const installationEntry = await update(installations.appConfig, oldEntry => {\n    const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n    const entryWithPromise = triggerRegistrationIfNecessary(\n      installations,\n      installationEntry\n    );\n    registrationPromise = entryWithPromise.registrationPromise;\n    return entryWithPromise.installationEntry;\n  });\n\n  if (installationEntry.fid === INVALID_FID) {\n    // FID generation failed. Waiting for the FID from the server.\n    return { installationEntry: await registrationPromise! };\n  }\n\n  return {\n    installationEntry,\n    registrationPromise\n  };\n}\n\n/**\n * Creates a new Installation Entry if one does not exist.\n * Also clears timed out pending requests.\n */\nfunction updateOrCreateInstallationEntry(\n  oldEntry: InstallationEntry | undefined\n): InstallationEntry {\n  const entry: InstallationEntry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: RequestStatus.NOT_STARTED\n  };\n\n  return clearTimedOutRequest(entry);\n}\n\n/**\n * If the Firebase Installation is not registered yet, this will trigger the\n * registration and return an InProgressInstallationEntry.\n *\n * If registrationPromise does not exist, the installationEntry is guaranteed\n * to be registered.\n */\nfunction triggerRegistrationIfNecessary(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InstallationEntry\n): InstallationEntryWithRegistrationPromise {\n  if (installationEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(\n        ERROR_FACTORY.create(ErrorCode.APP_OFFLINE)\n      );\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry: InProgressInstallationEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: RequestStatus.IN_PROGRESS,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(\n      installations,\n      inProgressEntry\n    );\n    return { installationEntry: inProgressEntry, registrationPromise };\n  } else if (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS\n  ) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return { installationEntry };\n  }\n}\n\n/** This will be executed only once for each new Firebase Installation. */\nasync function registerInstallation(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  try {\n    const registeredInstallationEntry = await createInstallationRequest(\n      installations,\n      installationEntry\n    );\n    return set(installations.appConfig, registeredInstallationEntry);\n  } catch (e) {\n    if (isServerError(e) && e.customData.serverCode === 409) {\n      // Server returned a \"FID cannot be used\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      // Registration failed. Set FID as not registered.\n      await set(installations.appConfig, {\n        fid: installationEntry.fid,\n        registrationStatus: RequestStatus.NOT_STARTED\n      });\n    }\n    throw e;\n  }\n}\n\n/** Call if FID registration is pending in another request. */\nasync function waitUntilFidRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<RegisteredInstallationEntry> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry: InstallationEntry = await updateInstallationRequest(\n    installations.appConfig\n  );\n  while (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n    // createInstallation request still in progress.\n    await sleep(100);\n\n    entry = await updateInstallationRequest(installations.appConfig);\n  }\n\n  if (entry.registrationStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    const { installationEntry, registrationPromise } =\n      await getInstallationEntry(installations);\n\n    if (registrationPromise) {\n      return registrationPromise;\n    } else {\n      // if there is no registrationPromise, entry is registered.\n      return installationEntry as RegisteredInstallationEntry;\n    }\n  }\n\n  return entry;\n}\n\n/**\n * Called only if there is a CreateInstallation request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * CreateInstallation request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateInstallationRequest(\n  appConfig: AppConfig\n): Promise<InstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(ErrorCode.INSTALLATION_NOT_FOUND);\n    }\n    return clearTimedOutRequest(oldEntry);\n  });\n}\n\nfunction clearTimedOutRequest(entry: InstallationEntry): InstallationEntry {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: RequestStatus.NOT_STARTED\n    };\n  }\n\n  return entry;\n}\n\nfunction hasInstallationRequestTimedOut(\n  installationEntry: InstallationEntry\n): boolean {\n  return (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS &&\n    installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry\n} from '../interfaces/installation-entry';\nimport { PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport {\n  FirebaseInstallationsImpl,\n  AppConfig\n} from '../interfaces/installation-impl';\n\nexport async function generateAuthTokenRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    installation: {\n      sdkVersion: PACKAGE_VERSION,\n      appId: appConfig.appId\n    }\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: GenerateAuthTokenResponse = await response.json();\n    const completedAuthToken: CompletedAuthToken =\n      extractAuthTokenInfoFromResponse(responseValue);\n    return completedAuthToken;\n  } else {\n    throw await getErrorFromResponse('Generate Auth Token', response);\n  }\n}\n\nfunction getGenerateAuthTokenEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { generateAuthTokenRequest } from '../functions/generate-auth-token-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  AuthToken,\n  CompletedAuthToken,\n  InProgressAuthToken,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS, TOKEN_EXPIRATION_BUFFER } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { remove, set, update } from './idb-manager';\n\n/**\n * Returns a valid authentication token for the installation. Generates a new\n * token if one doesn't exist, is expired or about to expire.\n *\n * Should only be called if the Firebase Installation is registered.\n */\nexport async function refreshAuthToken(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh = false\n): Promise<CompletedAuthToken> {\n  let tokenPromise: Promise<CompletedAuthToken> | undefined;\n  const entry = await update(installations.appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n      // There is a valid token in the DB.\n      return oldEntry;\n    } else if (oldAuthToken.requestStatus === RequestStatus.IN_PROGRESS) {\n      // There already is a token request in progress.\n      tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n      return oldEntry;\n    } else {\n      // No token or token expired.\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      }\n\n      const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n      tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n      return inProgressEntry;\n    }\n  });\n\n  const authToken = tokenPromise\n    ? await tokenPromise\n    : (entry.authToken as CompletedAuthToken);\n  return authToken;\n}\n\n/**\n * Call only if FID is registered and Auth Token request is in progress.\n *\n * Waits until the current pending request finishes. If the request times out,\n * tries once in this thread as well.\n */\nasync function waitUntilAuthTokenRequest(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh: boolean\n): Promise<CompletedAuthToken> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry = await updateAuthTokenRequest(installations.appConfig);\n  while (entry.authToken.requestStatus === RequestStatus.IN_PROGRESS) {\n    // generateAuthToken still in progress.\n    await sleep(100);\n\n    entry = await updateAuthTokenRequest(installations.appConfig);\n  }\n\n  const authToken = entry.authToken;\n  if (authToken.requestStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    return refreshAuthToken(installations, forceRefresh);\n  } else {\n    return authToken;\n  }\n}\n\n/**\n * Called only if there is a GenerateAuthToken request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * GenerateAuthToken request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateAuthTokenRequest(\n  appConfig: AppConfig\n): Promise<RegisteredInstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return {\n        ...oldEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n    }\n\n    return oldEntry;\n  });\n}\n\nasync function fetchAuthTokenFromServer(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  try {\n    const authToken = await generateAuthTokenRequest(\n      installations,\n      installationEntry\n    );\n    const updatedInstallationEntry: RegisteredInstallationEntry = {\n      ...installationEntry,\n      authToken\n    };\n    await set(installations.appConfig, updatedInstallationEntry);\n    return authToken;\n  } catch (e) {\n    if (\n      isServerError(e) &&\n      (e.customData.serverCode === 401 || e.customData.serverCode === 404)\n    ) {\n      // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      const updatedInstallationEntry: RegisteredInstallationEntry = {\n        ...installationEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n      await set(installations.appConfig, updatedInstallationEntry);\n    }\n    throw e;\n  }\n}\n\nfunction isEntryRegistered(\n  installationEntry: InstallationEntry | undefined\n): installationEntry is RegisteredInstallationEntry {\n  return (\n    installationEntry !== undefined &&\n    installationEntry.registrationStatus === RequestStatus.COMPLETED\n  );\n}\n\nfunction isAuthTokenValid(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.COMPLETED &&\n    !isAuthTokenExpired(authToken)\n  );\n}\n\nfunction isAuthTokenExpired(authToken: CompletedAuthToken): boolean {\n  const now = Date.now();\n  return (\n    now < authToken.creationTime ||\n    authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER\n  );\n}\n\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(\n  oldEntry: RegisteredInstallationEntry\n): RegisteredInstallationEntry {\n  const inProgressAuthToken: InProgressAuthToken = {\n    requestStatus: RequestStatus.IN_PROGRESS,\n    requestTime: Date.now()\n  };\n  return {\n    ...oldEntry,\n    authToken: inProgressAuthToken\n  };\n}\n\nfunction hasAuthTokenRequestTimedOut(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.IN_PROGRESS &&\n    authToken.requestTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Creates a Firebase Installation if there isn't one for the app and\n * returns the Installation ID.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function getId(installations: Installations): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  const { installationEntry, registrationPromise } = await getInstallationEntry(\n    installationsImpl\n  );\n\n  if (registrationPromise) {\n    registrationPromise.catch(console.error);\n  } else {\n    // If the installation is already registered, update the authentication\n    // token if needed.\n    refreshAuthToken(installationsImpl).catch(console.error);\n  }\n\n  return installationEntry.fid;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns a Firebase Installations auth token, identifying the current\n * Firebase Installation.\n * @param installations - The `Installations` instance.\n * @param forceRefresh - Force refresh regardless of token expiration.\n *\n * @public\n */\nexport async function getToken(\n  installations: Installations,\n  forceRefresh = false\n): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  await completeInstallationRegistration(installationsImpl);\n\n  // At this point we either have a Registered Installation in the DB, or we've\n  // already thrown an error.\n  const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\n  return authToken.token;\n}\n\nasync function completeInstallationRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<void> {\n  const { registrationPromise } = await getInstallationEntry(installations);\n\n  if (registrationPromise) {\n    // A createInstallation request is in progress. Wait until it finishes.\n    await registrationPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { RegisteredInstallationEntry } from '../interfaces/installation-entry';\nimport {\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\n\nexport async function deleteInstallationRequest(\n  appConfig: AppConfig,\n  installationEntry: RegisteredInstallationEntry\n): Promise<void> {\n  const endpoint = getDeleteEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n  const request: RequestInit = {\n    method: 'DELETE',\n    headers\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (!response.ok) {\n    throw await getErrorFromResponse('Delete Installation', response);\n  }\n}\n\nfunction getDeleteEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { deleteInstallationRequest } from '../functions/delete-installation-request';\nimport { remove, update } from '../helpers/idb-manager';\nimport { RequestStatus } from '../interfaces/installation-entry';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Deletes the Firebase Installation and all associated data.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function deleteInstallations(\n  installations: Installations\n): Promise<void> {\n  const { appConfig } = installations as FirebaseInstallationsImpl;\n\n  const entry = await update(appConfig, oldEntry => {\n    if (oldEntry && oldEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n      // Delete the unregistered entry without sending a deleteInstallation request.\n      return undefined;\n    }\n    return oldEntry;\n  });\n\n  if (entry) {\n    if (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n      // Can't delete while trying to register.\n      throw ERROR_FACTORY.create(ErrorCode.DELETE_PENDING_REGISTRATION);\n    } else if (entry.registrationStatus === RequestStatus.COMPLETED) {\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      } else {\n        await deleteInstallationRequest(appConfig, entry);\n        await remove(appConfig);\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { addCallback, removeCallback } from '../helpers/fid-changed';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * An user defined callback function that gets called when Installations ID changes.\n *\n * @public\n */\nexport type IdChangeCallbackFn = (installationId: string) => void;\n/**\n * Unsubscribe a callback function previously added via {@link IdChangeCallbackFn}.\n *\n * @public\n */\nexport type IdChangeUnsubscribeFn = () => void;\n\n/**\n * Sets a new callback that will get called when Installation ID changes.\n * Returns an unsubscribe function that will remove the callback when called.\n * @param installations - The `Installations` instance.\n * @param callback - The callback function that is invoked when FID changes.\n * @returns A function that can be called to unsubscribe.\n *\n * @public\n */\nexport function onIdChange(\n  installations: Installations,\n  callback: IdChangeCallbackFn\n): IdChangeUnsubscribeFn {\n  const { appConfig } = installations as FirebaseInstallationsImpl;\n\n  addCallback(appConfig, callback);\n  return () => {\n    removeCallback(appConfig, callback);\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns an instance of {@link Installations} associated with the given\n * {@link @firebase/app#FirebaseApp} instance.\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\n *\n * @public\n */\nexport function getInstallations(app: FirebaseApp = getApp()): Installations {\n  const installationsImpl = _getProvider(app, 'installations').getImmediate();\n  return installationsImpl;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport { FirebaseError } from '@firebase/util';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: Array<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId'\n  ];\n\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: app.options.projectId!,\n    apiKey: app.options.apiKey!,\n    appId: app.options.appId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, _getProvider } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer\n} from '@firebase/component';\nimport { getId, getToken } from '../api/index';\nimport { _FirebaseInstallationsInternal } from '../interfaces/public-types';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { extractAppConfig } from '../helpers/extract-app-config';\n\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\n\nconst publicFactory: InstanceFactory<'installations'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n\n  const installationsImpl: FirebaseInstallationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\n\nconst internalFactory: InstanceFactory<'installations-internal'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n\n  const installationsInternal: _FirebaseInstallationsInternal = {\n    getId: () => getId(installations),\n    getToken: (forceRefresh?: boolean) => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\n\nexport function registerInstallations(): void {\n  _registerComponent(\n    new Component(INSTALLATIONS_NAME, publicFactory, ComponentType.PUBLIC)\n  );\n  _registerComponent(\n    new Component(\n      INSTALLATIONS_NAME_INTERNAL,\n      internalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n}\n", "/**\n * The Firebase Installations Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerInstallations } from './functions/config';\nimport { registerVersion } from '@firebase/app';\nimport { name, version } from '../package.json';\n\nexport * from './api';\nexport * from './interfaces/public-types';\n\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\nregisterVersion(name, version, '__BUILD_TARGET__');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_SW_PATH = '/firebase-messaging-sw.js';\nexport const DEFAULT_SW_SCOPE = '/firebase-cloud-messaging-push-scope';\n\nexport const DEFAULT_VAPID_KEY =\n  'BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4';\n\nexport const ENDPOINT = 'https://fcmregistrations.googleapis.com/v1';\n\n/** Key of FCM Payload in Notification's data field. */\nexport const FCM_MSG = 'FCM_MSG';\n\nexport const CONSOLE_CAMPAIGN_ID = 'google.c.a.c_id';\nexport const CONSOLE_CAMPAIGN_NAME = 'google.c.a.c_l';\nexport const CONSOLE_CAMPAIGN_TIME = 'google.c.a.ts';\n/** Set to '1' if Analytics is enabled for the campaign */\nexport const CONSOLE_CAMPAIGN_ANALYTICS_ENABLED = 'google.c.a.e';\nexport const TAG = 'FirebaseMessaging: ';\nexport const MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST = 1000;\nexport const MAX_RETRIES = 3;\nexport const LOG_INTERVAL_IN_MS = 86400000; //24 hour\nexport const DEFAULT_BACKOFF_TIME_MS = 5000;\n\n// FCM log source name registered at Firelog: 'FCM_CLIENT_EVENT_LOGGING'. It uniquely identifies\n// FCM's logging configuration.\nexport const FCM_LOG_SOURCE = 1249;\n\n// Defined as in proto/messaging_event.proto. Neglecting fields that are supported.\nexport const SDK_PLATFORM_WEB = 3;\nexport const EVENT_MESSAGE_DELIVERED = 1;\n\nexport enum MessageType {\n  DATA_MESSAGE = 1,\n  DISPLAY_NOTIFICATION = 3\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n * in compliance with the License. You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under the License\n * is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n * or implied. See the License for the specific language governing permissions and limitations under\n * the License.\n */\n\nimport {\n  CONSOLE_CAMPAIGN_ANALYTICS_ENABLED,\n  CONSOLE_CAMPAIGN_ID,\n  CONSOLE_CAMPAIGN_NAME,\n  CONSOLE_CAMPAIGN_TIME\n} from '../util/constants';\n\nexport interface MessagePayloadInternal {\n  notification?: NotificationPayloadInternal;\n  data?: unknown;\n  fcmOptions?: FcmOptionsInternal;\n  messageType?: MessageType;\n  isFirebaseMessaging?: boolean;\n  from: string;\n  fcmMessageId: string;\n  productId: number;\n  // eslint-disable-next-line camelcase\n  collapse_key: string;\n}\n\nexport interface NotificationPayloadInternal extends NotificationOptions {\n  title: string;\n  // Supported in the Legacy Send API.\n  // See:https://firebase.google.com/docs/cloud-messaging/xmpp-server-ref.\n  // eslint-disable-next-line camelcase\n  click_action?: string;\n  icon?: string;\n}\n\n// Defined in\n// https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages#webpushfcmoptions. Note\n// that the keys are sent to the clients in snake cases which we need to convert to camel so it can\n// be exposed as a type to match the Firebase API convention.\nexport interface FcmOptionsInternal {\n  link?: string;\n\n  // eslint-disable-next-line camelcase\n  analytics_label?: string;\n}\n\nexport enum MessageType {\n  PUSH_RECEIVED = 'push-received',\n  NOTIFICATION_CLICKED = 'notification-clicked'\n}\n\n/** Additional data of a message sent from the FN Console. */\nexport interface ConsoleMessageData {\n  [CONSOLE_CAMPAIGN_ID]: string;\n  [CONSOLE_CAMPAIGN_TIME]: string;\n  [CONSOLE_CAMPAIGN_NAME]?: string;\n  [CONSOLE_CAMPAIGN_ANALYTICS_ENABLED]?: '1';\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function arrayToBase64(array: Uint8Array | ArrayBuffer): string {\n  const uint8Array = new Uint8Array(array);\n  const base64String = btoa(String.fromCharCode(...uint8Array));\n  return base64String.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n\nexport function base64ToArray(base64String: string): Uint8Array {\n  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);\n  const base64 = (base64String + padding)\n    .replace(/\\-/g, '+')\n    .replace(/_/g, '/');\n\n  const rawData = atob(base64);\n  const outputArray = new Uint8Array(rawData.length);\n\n  for (let i = 0; i < rawData.length; ++i) {\n    outputArray[i] = rawData.charCodeAt(i);\n  }\n  return outputArray;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { deleteDB, openDB } from 'idb';\n\nimport { TokenDetails } from '../interfaces/token-details';\nimport { arrayToBase64 } from './array-base64-translator';\n\n// https://github.com/firebase/firebase-js-sdk/blob/7857c212f944a2a9eb421fd4cb7370181bc034b5/packages/messaging/src/interfaces/token-details.ts\nexport interface V2TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: string | Uint8Array;\n  subscription: PushSubscription;\n  fcmSenderId: string;\n  fcmPushSet: string;\n  createTime?: number;\n  endpoint?: string;\n  auth?: string;\n  p256dh?: string;\n}\n\n// https://github.com/firebase/firebase-js-sdk/blob/6b5b15ce4ea3df5df5df8a8b33a4e41e249c7715/packages/messaging/src/interfaces/token-details.ts\nexport interface V3TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: Uint8Array;\n  fcmSenderId: string;\n  fcmPushSet: string;\n  endpoint: string;\n  auth: ArrayBuffer;\n  p256dh: ArrayBuffer;\n  createTime: number;\n}\n\n// https://github.com/firebase/firebase-js-sdk/blob/9567dba664732f681fa7fe60f5b7032bb1daf4c9/packages/messaging/src/interfaces/token-details.ts\nexport interface V4TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: Uint8Array;\n  fcmSenderId: string;\n  endpoint: string;\n  auth: ArrayBufferLike;\n  p256dh: ArrayBufferLike;\n  createTime: number;\n}\n\nconst OLD_DB_NAME = 'fcm_token_details_db';\n/**\n * The last DB version of 'fcm_token_details_db' was 4. This is one higher, so that the upgrade\n * callback is called for all versions of the old DB.\n */\nconst OLD_DB_VERSION = 5;\nconst OLD_OBJECT_STORE_NAME = 'fcm_token_object_Store';\n\nexport async function migrateOldDatabase(\n  senderId: string\n): Promise<TokenDetails | null> {\n  if ('databases' in indexedDB) {\n    // indexedDb.databases() is an IndexedDB v3 API and does not exist in all browsers. TODO: Remove\n    // typecast when it lands in TS types.\n    const databases = await (\n      indexedDB as {\n        databases(): Promise<Array<{ name: string; version: number }>>;\n      }\n    ).databases();\n    const dbNames = databases.map(db => db.name);\n\n    if (!dbNames.includes(OLD_DB_NAME)) {\n      // old DB didn't exist, no need to open.\n      return null;\n    }\n  }\n\n  let tokenDetails: TokenDetails | null = null;\n\n  const db = await openDB(OLD_DB_NAME, OLD_DB_VERSION, {\n    upgrade: async (db, oldVersion, newVersion, upgradeTransaction) => {\n      if (oldVersion < 2) {\n        // Database too old, skip migration.\n        return;\n      }\n\n      if (!db.objectStoreNames.contains(OLD_OBJECT_STORE_NAME)) {\n        // Database did not exist. Nothing to do.\n        return;\n      }\n\n      const objectStore = upgradeTransaction.objectStore(OLD_OBJECT_STORE_NAME);\n      const value = await objectStore.index('fcmSenderId').get(senderId);\n      await objectStore.clear();\n\n      if (!value) {\n        // No entry in the database, nothing to migrate.\n        return;\n      }\n\n      if (oldVersion === 2) {\n        const oldDetails = value as V2TokenDetails;\n\n        if (!oldDetails.auth || !oldDetails.p256dh || !oldDetails.endpoint) {\n          return;\n        }\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime ?? Date.now(),\n          subscriptionOptions: {\n            auth: oldDetails.auth,\n            p256dh: oldDetails.p256dh,\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey:\n              typeof oldDetails.vapidKey === 'string'\n                ? oldDetails.vapidKey\n                : arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      } else if (oldVersion === 3) {\n        const oldDetails = value as V3TokenDetails;\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime,\n          subscriptionOptions: {\n            auth: arrayToBase64(oldDetails.auth),\n            p256dh: arrayToBase64(oldDetails.p256dh),\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      } else if (oldVersion === 4) {\n        const oldDetails = value as V4TokenDetails;\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime,\n          subscriptionOptions: {\n            auth: arrayToBase64(oldDetails.auth),\n            p256dh: arrayToBase64(oldDetails.p256dh),\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      }\n    }\n  });\n  db.close();\n\n  // Delete all old databases.\n  await deleteDB(OLD_DB_NAME);\n  await deleteDB('fcm_vapid_details_db');\n  await deleteDB('undefined');\n\n  return checkTokenDetails(tokenDetails) ? tokenDetails : null;\n}\n\nfunction checkTokenDetails(\n  tokenDetails: TokenDetails | null\n): tokenDetails is TokenDetails {\n  if (!tokenDetails || !tokenDetails.subscriptionOptions) {\n    return false;\n  }\n  const { subscriptionOptions } = tokenDetails;\n  return (\n    typeof tokenDetails.createTime === 'number' &&\n    tokenDetails.createTime > 0 &&\n    typeof tokenDetails.token === 'string' &&\n    tokenDetails.token.length > 0 &&\n    typeof subscriptionOptions.auth === 'string' &&\n    subscriptionOptions.auth.length > 0 &&\n    typeof subscriptionOptions.p256dh === 'string' &&\n    subscriptionOptions.p256dh.length > 0 &&\n    typeof subscriptionOptions.endpoint === 'string' &&\n    subscriptionOptions.endpoint.length > 0 &&\n    typeof subscriptionOptions.swScope === 'string' &&\n    subscriptionOptions.swScope.length > 0 &&\n    typeof subscriptionOptions.vapidKey === 'string' &&\n    subscriptionOptions.vapidKey.length > 0\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, deleteDB, openDB } from 'idb';\n\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\nimport { TokenDetails } from '../interfaces/token-details';\nimport { migrateOldDatabase } from '../helpers/migrate-old-database';\n\n// Exported for tests.\nexport const DATABASE_NAME = 'firebase-messaging-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-messaging-store';\n\ninterface MessagingDB extends DBSchema {\n  'firebase-messaging-store': {\n    key: string;\n    value: TokenDetails;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<MessagingDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<MessagingDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (upgradeDb, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through behavior is what we want,\n        // because if there are multiple versions between the old version and the current version, we\n        // want ALL the migrations that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            upgradeDb.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function dbGet(\n  firebaseDependencies: FirebaseInternalDependencies\n): Promise<TokenDetails | undefined> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tokenDetails = (await db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key)) as TokenDetails;\n\n  if (tokenDetails) {\n    return tokenDetails;\n  } else {\n    // Check if there is a tokenDetails object in the old DB.\n    const oldTokenDetails = await migrateOldDatabase(\n      firebaseDependencies.appConfig.senderId\n    );\n    if (oldTokenDetails) {\n      await dbSet(firebaseDependencies, oldTokenDetails);\n      return oldTokenDetails;\n    }\n  }\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function dbSet(\n  firebaseDependencies: FirebaseInternalDependencies,\n  tokenDetails: TokenDetails\n): Promise<TokenDetails> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).put(tokenDetails, key);\n  await tx.done;\n  return tokenDetails;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function dbRemove(\n  firebaseDependencies: FirebaseInternalDependencies\n): Promise<void> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/** Deletes the DB. Useful for tests. */\nexport async function dbDelete(): Promise<void> {\n  if (dbPromise) {\n    (await dbPromise).close();\n    await deleteDB(DATABASE_NAME);\n    dbPromise = null;\n  }\n}\n\nfunction getKey({ appConfig }: FirebaseInternalDependencies): string {\n  return appConfig.appId;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  AVAILABLE_IN_WINDOW = 'only-available-in-window',\n  AVAILABLE_IN_SW = 'only-available-in-sw',\n  PERMISSION_DEFAULT = 'permission-default',\n  PERMISSION_BLOCKED = 'permission-blocked',\n  UNSUPPORTED_BROWSER = 'unsupported-browser',\n  INDEXED_DB_UNSUPPORTED = 'indexed-db-unsupported',\n  FAILED_DEFAULT_REGISTRATION = 'failed-service-worker-registration',\n  TOKEN_SUBSCRIBE_FAILED = 'token-subscribe-failed',\n  TOKEN_SUBSCRIBE_NO_TOKEN = 'token-subscribe-no-token',\n  TOKEN_UNSUBSCRIBE_FAILED = 'token-unsubscribe-failed',\n  TOKEN_UPDATE_FAILED = 'token-update-failed',\n  TOKEN_UPDATE_NO_TOKEN = 'token-update-no-token',\n  INVALID_BG_HANDLER = 'invalid-bg-handler',\n  USE_SW_AFTER_GET_TOKEN = 'use-sw-after-get-token',\n  INVALID_SW_REGISTRATION = 'invalid-sw-registration',\n  USE_VAPID_KEY_AFTER_GET_TOKEN = 'use-vapid-key-after-get-token',\n  INVALID_VAPID_KEY = 'invalid-vapid-key'\n}\n\nexport const ERROR_MAP: ErrorMap<ErrorCode> = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.AVAILABLE_IN_WINDOW]:\n    'This method is available in a Window context.',\n  [ErrorCode.AVAILABLE_IN_SW]:\n    'This method is available in a service worker context.',\n  [ErrorCode.PERMISSION_DEFAULT]:\n    'The notification permission was not granted and dismissed instead.',\n  [ErrorCode.PERMISSION_BLOCKED]:\n    'The notification permission was not granted and blocked instead.',\n  [ErrorCode.UNSUPPORTED_BROWSER]:\n    \"This browser doesn't support the API's required to use the Firebase SDK.\",\n  [ErrorCode.INDEXED_DB_UNSUPPORTED]:\n    \"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)\",\n  [ErrorCode.FAILED_DEFAULT_REGISTRATION]:\n    'We are unable to register the default service worker. {$browserErrorMessage}',\n  [ErrorCode.TOKEN_SUBSCRIBE_FAILED]:\n    'A problem occurred while subscribing the user to FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN]:\n    'FCM returned no token when subscribing the user to push.',\n  [ErrorCode.TOKEN_UNSUBSCRIBE_FAILED]:\n    'A problem occurred while unsubscribing the ' +\n    'user from FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_UPDATE_FAILED]:\n    'A problem occurred while updating the user from FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_UPDATE_NO_TOKEN]:\n    'FCM returned no token when updating the user to push.',\n  [ErrorCode.USE_SW_AFTER_GET_TOKEN]:\n    'The useServiceWorker() method may only be called once and must be ' +\n    'called before calling getToken() to ensure your service worker is used.',\n  [ErrorCode.INVALID_SW_REGISTRATION]:\n    'The input to useServiceWorker() must be a ServiceWorkerRegistration.',\n  [ErrorCode.INVALID_BG_HANDLER]:\n    'The input to setBackgroundMessageHandler() must be a function.',\n  [ErrorCode.INVALID_VAPID_KEY]: 'The public VAPID key must be a string.',\n  [ErrorCode.USE_VAPID_KEY_AFTER_GET_TOKEN]:\n    'The usePublicVapidKey() method may only be called once and must be ' +\n    'called before calling getToken() to ensure your VAPID key is used.'\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.FAILED_DEFAULT_REGISTRATION]: { browserErrorMessage: string };\n  [ErrorCode.TOKEN_SUBSCRIBE_FAILED]: { errorInfo: string };\n  [ErrorCode.TOKEN_UNSUBSCRIBE_FAILED]: { errorInfo: string };\n  [ErrorCode.TOKEN_UPDATE_FAILED]: { errorInfo: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  'messaging',\n  'Messaging',\n  ERROR_MAP\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_VAPID_KEY, ENDPOINT } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { SubscriptionOptions, TokenDetails } from '../interfaces/token-details';\n\nimport { AppConfig } from '../interfaces/app-config';\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\n\nexport interface ApiResponse {\n  token?: string;\n  error?: { message: string };\n}\n\nexport interface ApiRequestBody {\n  web: {\n    endpoint: string;\n    p256dh: string;\n    auth: string;\n    applicationPubKey?: string;\n  };\n}\n\nexport async function requestGetToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  subscriptionOptions: SubscriptionOptions\n): Promise<string> {\n  const headers = await getHeaders(firebaseDependencies);\n  const body = getBody(subscriptionOptions);\n\n  const subscribeOptions = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  let responseData: ApiResponse;\n  try {\n    const response = await fetch(\n      getEndpoint(firebaseDependencies.appConfig),\n      subscribeOptions\n    );\n    responseData = await response.json();\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n\n  if (responseData.error) {\n    const message = responseData.error.message;\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_FAILED, {\n      errorInfo: message\n    });\n  }\n\n  if (!responseData.token) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN);\n  }\n\n  return responseData.token;\n}\n\nexport async function requestUpdateToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  tokenDetails: TokenDetails\n): Promise<string> {\n  const headers = await getHeaders(firebaseDependencies);\n  const body = getBody(tokenDetails.subscriptionOptions!);\n\n  const updateOptions = {\n    method: 'PATCH',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  let responseData: ApiResponse;\n  try {\n    const response = await fetch(\n      `${getEndpoint(firebaseDependencies.appConfig)}/${tokenDetails.token}`,\n      updateOptions\n    );\n    responseData = await response.json();\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n\n  if (responseData.error) {\n    const message = responseData.error.message;\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_FAILED, {\n      errorInfo: message\n    });\n  }\n\n  if (!responseData.token) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_NO_TOKEN);\n  }\n\n  return responseData.token;\n}\n\nexport async function requestDeleteToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  token: string\n): Promise<void> {\n  const headers = await getHeaders(firebaseDependencies);\n\n  const unsubscribeOptions = {\n    method: 'DELETE',\n    headers\n  };\n\n  try {\n    const response = await fetch(\n      `${getEndpoint(firebaseDependencies.appConfig)}/${token}`,\n      unsubscribeOptions\n    );\n    const responseData: ApiResponse = await response.json();\n    if (responseData.error) {\n      const message = responseData.error.message;\n      throw ERROR_FACTORY.create(ErrorCode.TOKEN_UNSUBSCRIBE_FAILED, {\n        errorInfo: message\n      });\n    }\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UNSUBSCRIBE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n}\n\nfunction getEndpoint({ projectId }: AppConfig): string {\n  return `${ENDPOINT}/projects/${projectId!}/registrations`;\n}\n\nasync function getHeaders({\n  appConfig,\n  installations\n}: FirebaseInternalDependencies): Promise<Headers> {\n  const authToken = await installations.getToken();\n\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': appConfig.apiKey!,\n    'x-goog-firebase-installations-auth': `FIS ${authToken}`\n  });\n}\n\nfunction getBody({\n  p256dh,\n  auth,\n  endpoint,\n  vapidKey\n}: SubscriptionOptions): ApiRequestBody {\n  const body: ApiRequestBody = {\n    web: {\n      endpoint,\n      auth,\n      p256dh\n    }\n  };\n\n  if (vapidKey !== DEFAULT_VAPID_KEY) {\n    body.web.applicationPubKey = vapidKey;\n  }\n\n  return body;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SubscriptionOptions, TokenDetails } from '../interfaces/token-details';\nimport {\n  arrayToBase64,\n  base64ToArray\n} from '../helpers/array-base64-translator';\nimport { dbGet, dbRemove, dbSet } from './idb-manager';\nimport {\n  requestDeleteToken,\n  requestGetToken,\n  requestUpdateToken\n} from './requests';\n\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\nimport { MessagingService } from '../messaging-service';\n\n// UpdateRegistration will be called once every week.\nconst TOKEN_EXPIRATION_MS = 7 * 24 * 60 * 60 * 1000; // 7 days\n\nexport async function getTokenInternal(\n  messaging: MessagingService\n): Promise<string> {\n  const pushSubscription = await getPushSubscription(\n    messaging.swRegistration!,\n    messaging.vapidKey!\n  );\n\n  const subscriptionOptions: SubscriptionOptions = {\n    vapidKey: messaging.vapidKey!,\n    swScope: messaging.swRegistration!.scope,\n    endpoint: pushSubscription.endpoint,\n    auth: arrayToBase64(pushSubscription.getKey('auth')!),\n    p256dh: arrayToBase64(pushSubscription.getKey('p256dh')!)\n  };\n\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  if (!tokenDetails) {\n    // No token, get a new one.\n    return getNewToken(messaging.firebaseDependencies, subscriptionOptions);\n  } else if (\n    !isTokenValid(tokenDetails.subscriptionOptions!, subscriptionOptions)\n  ) {\n    // Invalid token, get a new one.\n    try {\n      await requestDeleteToken(\n        messaging.firebaseDependencies!,\n        tokenDetails.token\n      );\n    } catch (e) {\n      // Suppress errors because of #2364\n      console.warn(e);\n    }\n\n    return getNewToken(messaging.firebaseDependencies!, subscriptionOptions);\n  } else if (Date.now() >= tokenDetails.createTime + TOKEN_EXPIRATION_MS) {\n    // Weekly token refresh\n    return updateToken(messaging, {\n      token: tokenDetails.token,\n      createTime: Date.now(),\n      subscriptionOptions\n    });\n  } else {\n    // Valid token, nothing to do.\n    return tokenDetails.token;\n  }\n}\n\n/**\n * This method deletes the token from the database, unsubscribes the token from FCM, and unregisters\n * the push subscription if it exists.\n */\nexport async function deleteTokenInternal(\n  messaging: MessagingService\n): Promise<boolean> {\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  if (tokenDetails) {\n    await requestDeleteToken(\n      messaging.firebaseDependencies,\n      tokenDetails.token\n    );\n    await dbRemove(messaging.firebaseDependencies);\n  }\n\n  // Unsubscribe from the push subscription.\n  const pushSubscription =\n    await messaging.swRegistration!.pushManager.getSubscription();\n  if (pushSubscription) {\n    return pushSubscription.unsubscribe();\n  }\n\n  // If there's no SW, consider it a success.\n  return true;\n}\n\nasync function updateToken(\n  messaging: MessagingService,\n  tokenDetails: TokenDetails\n): Promise<string> {\n  try {\n    const updatedToken = await requestUpdateToken(\n      messaging.firebaseDependencies,\n      tokenDetails\n    );\n\n    const updatedTokenDetails: TokenDetails = {\n      ...tokenDetails,\n      token: updatedToken,\n      createTime: Date.now()\n    };\n\n    await dbSet(messaging.firebaseDependencies, updatedTokenDetails);\n    return updatedToken;\n  } catch (e) {\n    throw e;\n  }\n}\n\nasync function getNewToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  subscriptionOptions: SubscriptionOptions\n): Promise<string> {\n  const token = await requestGetToken(\n    firebaseDependencies,\n    subscriptionOptions\n  );\n  const tokenDetails: TokenDetails = {\n    token,\n    createTime: Date.now(),\n    subscriptionOptions\n  };\n  await dbSet(firebaseDependencies, tokenDetails);\n  return tokenDetails.token;\n}\n\n/**\n * Gets a PushSubscription for the current user.\n */\nasync function getPushSubscription(\n  swRegistration: ServiceWorkerRegistration,\n  vapidKey: string\n): Promise<PushSubscription> {\n  const subscription = await swRegistration.pushManager.getSubscription();\n  if (subscription) {\n    return subscription;\n  }\n\n  return swRegistration.pushManager.subscribe({\n    userVisibleOnly: true,\n    // Chrome <= 75 doesn't support base64-encoded VAPID key. For backward compatibility, VAPID key\n    // submitted to pushManager#subscribe must be of type Uint8Array.\n    applicationServerKey: base64ToArray(vapidKey)\n  });\n}\n\n/**\n * Checks if the saved tokenDetails object matches the configuration provided.\n */\nfunction isTokenValid(\n  dbOptions: SubscriptionOptions,\n  currentOptions: SubscriptionOptions\n): boolean {\n  const isVapidKeyEqual = currentOptions.vapidKey === dbOptions.vapidKey;\n  const isEndpointEqual = currentOptions.endpoint === dbOptions.endpoint;\n  const isAuthEqual = currentOptions.auth === dbOptions.auth;\n  const isP256dhEqual = currentOptions.p256dh === dbOptions.p256dh;\n\n  return isVapidKeyEqual && isEndpointEqual && isAuthEqual && isP256dhEqual;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MessagePayload } from '../interfaces/public-types';\nimport { MessagePayloadInternal } from '../interfaces/internal-message-payload';\n\nexport function externalizePayload(\n  internalPayload: MessagePayloadInternal\n): MessagePayload {\n  const payload: MessagePayload = {\n    from: internalPayload.from,\n    // eslint-disable-next-line camelcase\n    collapseKey: internalPayload.collapse_key,\n    // eslint-disable-next-line camelcase\n    messageId: internalPayload.fcmMessageId\n  } as MessagePayload;\n\n  propagateNotificationPayload(payload, internalPayload);\n  propagateDataPayload(payload, internalPayload);\n  propagateFcmOptions(payload, internalPayload);\n\n  return payload;\n}\n\nfunction propagateNotificationPayload(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  if (!messagePayloadInternal.notification) {\n    return;\n  }\n\n  payload.notification = {};\n\n  const title = messagePayloadInternal.notification!.title;\n  if (!!title) {\n    payload.notification!.title = title;\n  }\n\n  const body = messagePayloadInternal.notification!.body;\n  if (!!body) {\n    payload.notification!.body = body;\n  }\n\n  const image = messagePayloadInternal.notification!.image;\n  if (!!image) {\n    payload.notification!.image = image;\n  }\n\n  const icon = messagePayloadInternal.notification!.icon;\n  if (!!icon) {\n    payload.notification!.icon = icon;\n  }\n}\n\nfunction propagateDataPayload(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  if (!messagePayloadInternal.data) {\n    return;\n  }\n\n  payload.data = messagePayloadInternal.data as { [key: string]: string };\n}\n\nfunction propagateFcmOptions(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  // fcmOptions.link value is written into notification.click_action. see more in b/232072111\n  if (\n    !messagePayloadInternal.fcmOptions &&\n    !messagePayloadInternal.notification?.click_action\n  ) {\n    return;\n  }\n\n  payload.fcmOptions = {};\n\n  const link =\n    messagePayloadInternal.fcmOptions?.link ??\n    messagePayloadInternal.notification?.click_action;\n\n  if (!!link) {\n    payload.fcmOptions!.link = link;\n  }\n\n  // eslint-disable-next-line camelcase\n  const analyticsLabel = messagePayloadInternal.fcmOptions?.analytics_label;\n  if (!!analyticsLabel) {\n    payload.fcmOptions!.analyticsLabel = analyticsLabel;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSOLE_CAMPAIGN_ID } from '../util/constants';\nimport { ConsoleMessageData } from '../interfaces/internal-message-payload';\n\nexport function isConsoleMessage(data: unknown): data is ConsoleMessageData {\n  // This message has a campaign ID, meaning it was sent using the Firebase Console.\n  return typeof data === 'object' && !!data && CONSOLE_CAMPAIGN_ID in data;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DEFAULT_BACKOFF_TIME_MS,\n  EVENT_MESSAGE_DELIVERED,\n  FCM_LOG_SOURCE,\n  LOG_INTERVAL_IN_MS,\n  MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST,\n  MAX_RETRIES,\n  MessageType,\n  SDK_PLATFORM_WEB\n} from '../util/constants';\nimport {\n  FcmEvent,\n  LogEvent,\n  LogRequest,\n  LogResponse,\n  ComplianceData\n} from '../interfaces/logging-types';\n\nimport { MessagePayloadInternal } from '../interfaces/internal-message-payload';\nimport { MessagingService } from '../messaging-service';\n\nconst LOG_ENDPOINT = 'https://play.google.com/log?format=json_proto3';\n\nconst FCM_TRANSPORT_KEY = _mergeStrings(\n  'AzSCbw63g1R0nCw85jG8',\n  'Iaya3yLKwmgvh7cF0q4'\n);\n\nexport function startLoggingService(messaging: MessagingService): void {\n  if (!messaging.isLogServiceStarted) {\n    _processQueue(messaging, LOG_INTERVAL_IN_MS);\n    messaging.isLogServiceStarted = true;\n  }\n}\n\n/**\n *\n * @param messaging the messaging instance.\n * @param offsetInMs this method execute after `offsetInMs` elapsed .\n */\nexport function _processQueue(\n  messaging: MessagingService,\n  offsetInMs: number\n): void {\n  setTimeout(async () => {\n    if (!messaging.deliveryMetricsExportedToBigQueryEnabled) {\n      // flush events and terminate logging service\n      messaging.logEvents = [];\n      messaging.isLogServiceStarted = false;\n\n      return;\n    }\n\n    if (!messaging.logEvents.length) {\n      return _processQueue(messaging, LOG_INTERVAL_IN_MS);\n    }\n\n    await _dispatchLogEvents(messaging);\n  }, offsetInMs);\n}\n\nexport async function _dispatchLogEvents(\n  messaging: MessagingService\n): Promise<void> {\n  for (\n    let i = 0, n = messaging.logEvents.length;\n    i < n;\n    i += MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST\n  ) {\n    const logRequest = _createLogRequest(\n      messaging.logEvents.slice(i, i + MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST)\n    );\n\n    let retryCount = 0,\n      response = {} as Response;\n\n    do {\n      try {\n        response = await fetch(\n          LOG_ENDPOINT.concat('&key=', FCM_TRANSPORT_KEY),\n          {\n            method: 'POST',\n            body: JSON.stringify(logRequest)\n          }\n        );\n\n        // don't retry on 200s or non retriable errors\n        if (response.ok || (!response.ok && !isRetriableError(response))) {\n          break;\n        }\n\n        if (!response.ok && isRetriableError(response)) {\n          // rethrow to retry with quota\n          throw new Error(\n            'a retriable Non-200 code is returned in fetch to Firelog endpoint. Retry'\n          );\n        }\n      } catch (error) {\n        const isLastAttempt = retryCount === MAX_RETRIES;\n        if (isLastAttempt) {\n          // existing the do-while interactive retry logic because retry quota has reached.\n          break;\n        }\n      }\n\n      let delayInMs: number;\n      try {\n        delayInMs = Number(\n          ((await response.json()) as LogResponse).nextRequestWaitMillis\n        );\n      } catch (e) {\n        delayInMs = DEFAULT_BACKOFF_TIME_MS;\n      }\n\n      await new Promise(resolve => setTimeout(resolve, delayInMs));\n\n      retryCount++;\n    } while (retryCount < MAX_RETRIES);\n  }\n\n  messaging.logEvents = [];\n  // schedule for next logging\n  _processQueue(messaging, LOG_INTERVAL_IN_MS);\n}\n\nfunction isRetriableError(response: Response): boolean {\n  const httpStatus = response.status;\n\n  return (\n    httpStatus === 429 ||\n    httpStatus === 500 ||\n    httpStatus === 503 ||\n    httpStatus === 504\n  );\n}\n\nexport async function stageLog(\n  messaging: MessagingService,\n  internalPayload: MessagePayloadInternal\n): Promise<void> {\n  const fcmEvent = createFcmEvent(\n    internalPayload,\n    await messaging.firebaseDependencies.installations.getId()\n  );\n\n  createAndEnqueueLogEvent(messaging, fcmEvent, internalPayload.productId);\n}\n\nfunction createFcmEvent(\n  internalPayload: MessagePayloadInternal,\n  fid: string\n): FcmEvent {\n  const fcmEvent = {} as FcmEvent;\n\n  /* eslint-disable camelcase */\n  // some fields should always be non-null. Still check to ensure.\n  if (!!internalPayload.from) {\n    fcmEvent.project_number = internalPayload.from;\n  }\n\n  if (!!internalPayload.fcmMessageId) {\n    fcmEvent.message_id = internalPayload.fcmMessageId;\n  }\n\n  fcmEvent.instance_id = fid;\n\n  if (!!internalPayload.notification) {\n    fcmEvent.message_type = MessageType.DISPLAY_NOTIFICATION.toString();\n  } else {\n    fcmEvent.message_type = MessageType.DATA_MESSAGE.toString();\n  }\n\n  fcmEvent.sdk_platform = SDK_PLATFORM_WEB.toString();\n  fcmEvent.package_name = self.origin.replace(/(^\\w+:|^)\\/\\//, '');\n\n  if (!!internalPayload.collapse_key) {\n    fcmEvent.collapse_key = internalPayload.collapse_key;\n  }\n\n  fcmEvent.event = EVENT_MESSAGE_DELIVERED.toString();\n\n  if (!!internalPayload.fcmOptions?.analytics_label) {\n    fcmEvent.analytics_label = internalPayload.fcmOptions?.analytics_label;\n  }\n\n  /* eslint-enable camelcase */\n  return fcmEvent;\n}\n\nfunction createAndEnqueueLogEvent(\n  messaging: MessagingService,\n  fcmEvent: FcmEvent,\n  productId: number\n): void {\n  const logEvent = {} as LogEvent;\n\n  /* eslint-disable camelcase */\n  logEvent.event_time_ms = Math.floor(Date.now()).toString();\n  logEvent.source_extension_json_proto3 = JSON.stringify({\n    messaging_client_event: fcmEvent\n  });\n\n  if (!!productId) {\n    logEvent.compliance_data = buildComplianceData(productId);\n  }\n  // eslint-disable-next-line camelcase\n\n  messaging.logEvents.push(logEvent);\n}\n\nfunction buildComplianceData(productId: number): ComplianceData {\n  const complianceData: ComplianceData = {\n    privacy_context: {\n      prequest: {\n        origin_associated_product_id: productId\n      }\n    }\n  };\n\n  return complianceData;\n}\n\nexport function _createLogRequest(logEventQueue: LogEvent[]): LogRequest {\n  const logRequest = {} as LogRequest;\n\n  /* eslint-disable camelcase */\n  logRequest.log_source = FCM_LOG_SOURCE.toString();\n  logRequest.log_event = logEventQueue;\n  /* eslint-enable camelcase */\n\n  return logRequest;\n}\n\nexport function _mergeStrings(s1: string, s2: string): string {\n  const resultArray = [];\n  for (let i = 0; i < s1.length; i++) {\n    resultArray.push(s1.charAt(i));\n    if (i < s2.length) {\n      resultArray.push(s2.charAt(i));\n    }\n  }\n\n  return resultArray.join('');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\n\nimport { AppConfig } from '../interfaces/app-config';\nimport { FirebaseError } from '@firebase/util';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration Object');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: ReadonlyArray<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId',\n    'messagingSenderId'\n  ];\n\n  const { options } = app;\n  for (const keyName of configKeys) {\n    if (!options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: options.projectId!,\n    apiKey: options.apiKey!,\n    appId: options.appId!,\n    senderId: options.messagingSenderId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport { MessagePayload, NextFn, Observer } from './interfaces/public-types';\n\nimport { FirebaseAnalyticsInternalName } from '@firebase/analytics-interop-types';\nimport { FirebaseInternalDependencies } from './interfaces/internal-dependencies';\nimport { LogEvent } from './interfaces/logging-types';\nimport { Provider } from '@firebase/component';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { extractAppConfig } from './helpers/extract-app-config';\n\nexport class MessagingService implements _FirebaseService {\n  readonly app!: FirebaseApp;\n  readonly firebaseDependencies!: FirebaseInternalDependencies;\n\n  swRegistration?: ServiceWorkerRegistration;\n  vapidKey?: string;\n  // logging is only done with end user consent. Default to false.\n  deliveryMetricsExportedToBigQueryEnabled: boolean = false;\n\n  onBackgroundMessageHandler:\n    | NextFn<MessagePayload>\n    | Observer<MessagePayload>\n    | null = null;\n\n  onMessageHandler: NextFn<MessagePayload> | Observer<MessagePayload> | null =\n    null;\n\n  logEvents: LogEvent[] = [];\n  isLogServiceStarted: boolean = false;\n\n  constructor(\n    app: FirebaseApp,\n    installations: _FirebaseInstallationsInternal,\n    analyticsProvider: Provider<FirebaseAnalyticsInternalName>\n  ) {\n    const appConfig = extractAppConfig(app);\n\n    this.firebaseDependencies = {\n      app,\n      appConfig,\n      installations,\n      analyticsProvider\n    };\n  }\n\n  _delete(): Promise<void> {\n    return Promise.resolve();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_SW_PATH, DEFAULT_SW_SCOPE } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\n\nexport async function registerDefaultSw(\n  messaging: MessagingService\n): Promise<void> {\n  try {\n    messaging.swRegistration = await navigator.serviceWorker.register(\n      DEFAULT_SW_PATH,\n      {\n        scope: DEFAULT_SW_SCOPE\n      }\n    );\n\n    // The timing when browser updates sw when sw has an update is unreliable from experiment. It\n    // leads to version conflict when the SDK upgrades to a newer version in the main page, but sw\n    // is stuck with the old version. For example,\n    // https://github.com/firebase/firebase-js-sdk/issues/2590 The following line reliably updates\n    // sw if there was an update.\n    messaging.swRegistration.update().catch(() => {\n      /* it is non blocking and we don't care if it failed */\n    });\n  } catch (e) {\n    throw ERROR_FACTORY.create(ErrorCode.FAILED_DEFAULT_REGISTRATION, {\n      browserErrorMessage: (e as Error)?.message\n    });\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\nimport { registerDefaultSw } from './registerDefaultSw';\n\nexport async function updateSwReg(\n  messaging: MessagingService,\n  swRegistration?: ServiceWorkerRegistration | undefined\n): Promise<void> {\n  if (!swRegistration && !messaging.swRegistration) {\n    await registerDefaultSw(messaging);\n  }\n\n  if (!swRegistration && !!messaging.swRegistration) {\n    return;\n  }\n\n  if (!(swRegistration instanceof ServiceWorkerRegistration)) {\n    throw ERROR_FACTORY.create(ErrorCode.INVALID_SW_REGISTRATION);\n  }\n\n  messaging.swRegistration = swRegistration;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_VAPID_KEY } from '../util/constants';\nimport { MessagingService } from '../messaging-service';\n\nexport async function updateVapidKey(\n  messaging: MessagingService,\n  vapidKey?: string | undefined\n): Promise<void> {\n  if (!!vapidKey) {\n    messaging.vapidKey = vapidKey;\n  } else if (!messaging.vapidKey) {\n    messaging.vapidKey = DEFAULT_VAPID_KEY;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\nimport { getTokenInternal } from '../internals/token-manager';\nimport { updateSwReg } from '../helpers/updateSwReg';\nimport { updateVapidKey } from '../helpers/updateVapidKey';\nimport { GetTokenOptions } from '../interfaces/public-types';\n\nexport async function getToken(\n  messaging: MessagingService,\n  options?: GetTokenOptions\n): Promise<string> {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_WINDOW);\n  }\n\n  if (Notification.permission === 'default') {\n    await Notification.requestPermission();\n  }\n\n  if (Notification.permission !== 'granted') {\n    throw ERROR_FACTORY.create(ErrorCode.PERMISSION_BLOCKED);\n  }\n\n  await updateVapidKey(messaging, options?.vapidKey);\n  await updateSwReg(messaging, options?.serviceWorkerRegistration);\n\n  return getTokenInternal(messaging);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CONSOLE_CAMPAIGN_ID,\n  CONSOLE_CAMPAIGN_NAME,\n  CONSOLE_CAMPAIGN_TIME\n} from '../util/constants';\nimport {\n  ConsoleMessageData,\n  MessageType\n} from '../interfaces/internal-message-payload';\n\nimport { MessagingService } from '../messaging-service';\n\nexport async function logToScion(\n  messaging: MessagingService,\n  messageType: MessageType,\n  data: ConsoleMessageData\n): Promise<void> {\n  const eventType = getEventType(messageType);\n  const analytics =\n    await messaging.firebaseDependencies.analyticsProvider.get();\n  analytics.logEvent(eventType, {\n    /* eslint-disable camelcase */\n    message_id: data[CONSOLE_CAMPAIGN_ID],\n    message_name: data[CONSOLE_CAMPAIGN_NAME],\n    message_time: data[CONSOLE_CAMPAIGN_TIME],\n    message_device_time: Math.floor(Date.now() / 1000)\n    /* eslint-enable camelcase */\n  });\n}\n\nfunction getEventType(messageType: MessageType): string {\n  switch (messageType) {\n    case MessageType.NOTIFICATION_CLICKED:\n      return 'notification_open';\n    case MessageType.PUSH_RECEIVED:\n      return 'notification_foreground';\n    default:\n      throw new Error();\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  MessagePayloadInternal,\n  MessageType\n} from '../interfaces/internal-message-payload';\n\nimport { CONSOLE_CAMPAIGN_ANALYTICS_ENABLED } from '../util/constants';\nimport { MessagingService } from '../messaging-service';\nimport { externalizePayload } from '../helpers/externalizePayload';\nimport { isConsoleMessage } from '../helpers/is-console-message';\nimport { logToScion } from '../helpers/logToScion';\n\nexport async function messageEventListener(\n  messaging: MessagingService,\n  event: MessageEvent\n): Promise<void> {\n  const internalPayload = event.data as MessagePayloadInternal;\n\n  if (!internalPayload.isFirebaseMessaging) {\n    return;\n  }\n\n  if (\n    messaging.onMessageHandler &&\n    internalPayload.messageType === MessageType.PUSH_RECEIVED\n  ) {\n    if (typeof messaging.onMessageHandler === 'function') {\n      messaging.onMessageHandler(externalizePayload(internalPayload));\n    } else {\n      messaging.onMessageHandler.next(externalizePayload(internalPayload));\n    }\n  }\n\n  // Log to Scion if applicable\n  const dataPayload = internalPayload.data;\n  if (\n    isConsoleMessage(dataPayload) &&\n    dataPayload[CONSOLE_CAMPAIGN_ANALYTICS_ENABLED] === '1'\n  ) {\n    await logToScion(messaging, internalPayload.messageType!, dataPayload);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstanceFactory\n} from '@firebase/component';\nimport {\n  onNotificationClick,\n  onPush,\n  onSubChange\n} from '../listeners/sw-listeners';\n\nimport { GetTokenOptions } from '../interfaces/public-types';\nimport { MessagingInternal } from '@firebase/messaging-interop-types';\nimport { MessagingService } from '../messaging-service';\nimport { ServiceWorkerGlobalScope } from '../util/sw-types';\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { getToken } from '../api/getToken';\nimport { messageEventListener } from '../listeners/window-listener';\n\nimport { name, version } from '../../package.json';\n\nconst WindowMessagingFactory: InstanceFactory<'messaging'> = (\n  container: ComponentContainer\n) => {\n  const messaging = new MessagingService(\n    container.getProvider('app').getImmediate(),\n    container.getProvider('installations-internal').getImmediate(),\n    container.getProvider('analytics-internal')\n  );\n\n  navigator.serviceWorker.addEventListener('message', e =>\n    messageEventListener(messaging as MessagingService, e)\n  );\n\n  return messaging;\n};\n\nconst WindowMessagingInternalFactory: InstanceFactory<'messaging-internal'> = (\n  container: ComponentContainer\n) => {\n  const messaging = container\n    .getProvider('messaging')\n    .getImmediate() as MessagingService;\n\n  const messagingInternal: MessagingInternal = {\n    getToken: (options?: GetTokenOptions) => getToken(messaging, options)\n  };\n\n  return messagingInternal;\n};\n\ndeclare const self: ServiceWorkerGlobalScope;\nconst SwMessagingFactory: InstanceFactory<'messaging'> = (\n  container: ComponentContainer\n) => {\n  const messaging = new MessagingService(\n    container.getProvider('app').getImmediate(),\n    container.getProvider('installations-internal').getImmediate(),\n    container.getProvider('analytics-internal')\n  );\n\n  self.addEventListener('push', e => {\n    e.waitUntil(onPush(e, messaging as MessagingService));\n  });\n  self.addEventListener('pushsubscriptionchange', e => {\n    e.waitUntil(onSubChange(e, messaging as MessagingService));\n  });\n  self.addEventListener('notificationclick', e => {\n    e.waitUntil(onNotificationClick(e));\n  });\n\n  return messaging;\n};\n\nexport function registerMessagingInWindow(): void {\n  _registerComponent(\n    new Component('messaging', WindowMessagingFactory, ComponentType.PUBLIC)\n  );\n\n  _registerComponent(\n    new Component(\n      'messaging-internal',\n      WindowMessagingInternalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\n/**\n * The messaging instance registered in sw is named differently than that of in client. This is\n * because both `registerMessagingInWindow` and `registerMessagingInSw` would be called in\n * `messaging-compat` and component with the same name can only be registered once.\n */\nexport function registerMessagingInSw(): void {\n  _registerComponent(\n    new Component('messaging-sw', SwMessagingFactory, ComponentType.PUBLIC)\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  areCookiesEnabled,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\n\n/**\n * Checks if all required APIs exist in the browser.\n * @returns a Promise that resolves to a boolean.\n *\n * @public\n */\nexport async function isWindowSupported(): Promise<boolean> {\n  try {\n    // This throws if open() is unsupported, so adding it to the conditional\n    // statement below can cause an uncaught error.\n    await validateIndexedDBOpenable();\n  } catch (e) {\n    return false;\n  }\n  // firebase-js-sdk/issues/2393 reveals that idb#open in Safari iframe and Firefox private browsing\n  // might be prohibited to run. In these contexts, an error would be thrown during the messaging\n  // instantiating phase, informing the developers to import/call isSupported for special handling.\n  return (\n    typeof window !== 'undefined' &&\n    isIndexedDBAvailable() &&\n    areCookiesEnabled() &&\n    'serviceWorker' in navigator &&\n    'PushManager' in window &&\n    'Notification' in window &&\n    'fetch' in window &&\n    ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n    PushSubscription.prototype.hasOwnProperty('getKey')\n  );\n}\n\n/**\n * Checks whether all required APIs exist within SW Context\n * @returns a Promise that resolves to a boolean.\n *\n * @public\n */\nexport async function isSwSupported(): Promise<boolean> {\n  // firebase-js-sdk/issues/2393 reveals that idb#open in Safari iframe and Firefox private browsing\n  // might be prohibited to run. In these contexts, an error would be thrown during the messaging\n  // instantiating phase, informing the developers to import/call isSupported for special handling.\n  return (\n    isIndexedDBAvailable() &&\n    (await validateIndexedDBOpenable()) &&\n    'PushManager' in self &&\n    'Notification' in self &&\n    ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n    PushSubscription.prototype.hasOwnProperty('getKey')\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\nimport { deleteTokenInternal } from '../internals/token-manager';\nimport { registerDefaultSw } from '../helpers/registerDefaultSw';\n\nexport async function deleteToken(\n  messaging: MessagingService\n): Promise<boolean> {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_WINDOW);\n  }\n\n  if (!messaging.swRegistration) {\n    await registerDefaultSw(messaging);\n  }\n\n  return deleteTokenInternal(messaging);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport {\n  MessagePayload,\n  NextFn,\n  Observer,\n  Unsubscribe\n} from '../interfaces/public-types';\nimport { MessagingService } from '../messaging-service';\n\nexport function onMessage(\n  messaging: MessagingService,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_WINDOW);\n  }\n\n  messaging.onMessageHandler = nextOrObserver;\n\n  return () => {\n    messaging.onMessageHandler = null;\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './util/errors';\nimport { FirebaseApp, _getProvider, getApp } from '@firebase/app';\nimport {\n  GetTokenOptions,\n  MessagePayload,\n  Messaging\n} from './interfaces/public-types';\nimport {\n  NextFn,\n  Observer,\n  Unsubscribe,\n  getModularInstance\n} from '@firebase/util';\nimport { isSwSupported, isWindowSupported } from './api/isSupported';\n\nimport { MessagingService } from './messaging-service';\nimport { deleteToken as _deleteToken } from './api/deleteToken';\nimport { getToken as _getToken } from './api/getToken';\nimport { onBackgroundMessage as _onBackgroundMessage } from './api/onBackgroundMessage';\nimport { onMessage as _onMessage } from './api/onMessage';\nimport { _setDeliveryMetricsExportedToBigQueryEnabled } from './api/setDeliveryMetricsExportedToBigQueryEnabled';\n\n/**\n * Retrieves a Firebase Cloud Messaging instance.\n *\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\n *\n * @public\n */\nexport function getMessagingInWindow(app: FirebaseApp = getApp()): Messaging {\n  // Conscious decision to make this async check non-blocking during the messaging instance\n  // initialization phase for performance consideration. An error would be thrown latter for\n  // developer's information. Developers can then choose to import and call `isSupported` for\n  // special handling.\n  isWindowSupported().then(\n    isSupported => {\n      // If `isWindowSupported()` resolved, but returned false.\n      if (!isSupported) {\n        throw ERROR_FACTORY.create(ErrorCode.UNSUPPORTED_BROWSER);\n      }\n    },\n    _ => {\n      // If `isWindowSupported()` rejected.\n      throw ERROR_FACTORY.create(ErrorCode.INDEXED_DB_UNSUPPORTED);\n    }\n  );\n  return _getProvider(getModularInstance(app), 'messaging').getImmediate();\n}\n\n/**\n * Retrieves a Firebase Cloud Messaging instance.\n *\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\n *\n * @public\n */\nexport function getMessagingInSw(app: FirebaseApp = getApp()): Messaging {\n  // Conscious decision to make this async check non-blocking during the messaging instance\n  // initialization phase for performance consideration. An error would be thrown latter for\n  // developer's information. Developers can then choose to import and call `isSupported` for\n  // special handling.\n  isSwSupported().then(\n    isSupported => {\n      // If `isSwSupported()` resolved, but returned false.\n      if (!isSupported) {\n        throw ERROR_FACTORY.create(ErrorCode.UNSUPPORTED_BROWSER);\n      }\n    },\n    _ => {\n      // If `isSwSupported()` rejected.\n      throw ERROR_FACTORY.create(ErrorCode.INDEXED_DB_UNSUPPORTED);\n    }\n  );\n  return _getProvider(getModularInstance(app), 'messaging-sw').getImmediate();\n}\n\n/**\n * Subscribes the {@link Messaging} instance to push notifications. Returns a Firebase Cloud\n * Messaging registration token that can be used to send push messages to that {@link Messaging}\n * instance.\n *\n * If notification permission isn't already granted, this method asks the user for permission. The\n * returned promise rejects if the user does not allow the app to show notifications.\n *\n * @param messaging - The {@link Messaging} instance.\n * @param options - Provides an optional vapid key and an optional service worker registration.\n *\n * @returns The promise resolves with an FCM registration token.\n *\n * @public\n */\nexport async function getToken(\n  messaging: Messaging,\n  options?: GetTokenOptions\n): Promise<string> {\n  messaging = getModularInstance(messaging);\n  return _getToken(messaging as MessagingService, options);\n}\n\n/**\n * Deletes the registration token associated with this {@link Messaging} instance and unsubscribes\n * the {@link Messaging} instance from the push subscription.\n *\n * @param messaging - The {@link Messaging} instance.\n *\n * @returns The promise resolves when the token has been successfully deleted.\n *\n * @public\n */\nexport function deleteToken(messaging: Messaging): Promise<boolean> {\n  messaging = getModularInstance(messaging);\n  return _deleteToken(messaging as MessagingService);\n}\n\n/**\n * When a push message is received and the user is currently on a page for your origin, the\n * message is passed to the page and an `onMessage()` event is dispatched with the payload of\n * the push message.\n *\n *\n * @param messaging - The {@link Messaging} instance.\n * @param nextOrObserver - This function, or observer object with `next` defined,\n *     is called when a message is received and the user is currently viewing your page.\n * @returns To stop listening for messages execute this returned function.\n *\n * @public\n */\nexport function onMessage(\n  messaging: Messaging,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  messaging = getModularInstance(messaging);\n  return _onMessage(messaging as MessagingService, nextOrObserver);\n}\n\n/**\n * Called when a message is received while the app is in the background. An app is considered to be\n * in the background if no active window is displayed.\n *\n * @param messaging - The {@link Messaging} instance.\n * @param nextOrObserver - This function, or observer object with `next` defined, is called when a\n * message is received and the app is currently in the background.\n *\n * @returns To stop listening for messages execute this returned function\n *\n * @public\n */\nexport function onBackgroundMessage(\n  messaging: Messaging,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  messaging = getModularInstance(messaging);\n  return _onBackgroundMessage(messaging as MessagingService, nextOrObserver);\n}\n\n/**\n * Enables or disables Firebase Cloud Messaging message delivery metrics export to BigQuery. By\n * default, message delivery metrics are not exported to BigQuery. Use this method to enable or\n * disable the export at runtime.\n *\n * @param messaging - The `FirebaseMessaging` instance.\n * @param enable - Whether Firebase Cloud Messaging should export message delivery metrics to\n * BigQuery.\n *\n * @public\n */\nexport function experimentalSetDeliveryMetricsExportedToBigQueryEnabled(\n  messaging: Messaging,\n  enable: boolean\n): void {\n  messaging = getModularInstance(messaging);\n  return _setDeliveryMetricsExportedToBigQueryEnabled(messaging, enable);\n}\n", "/**\n * The Firebase Cloud Messaging Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport '@firebase/installations';\n\nimport { Messaging } from './interfaces/public-types';\nimport { registerMessagingInWindow } from './helpers/register';\n\nexport {\n  getToken,\n  deleteToken,\n  onMessage,\n  getMessagingInWindow as getMessaging\n} from './api';\nexport { isWindowSupported as isSupported } from './api/isSupported';\nexport * from './interfaces/public-types';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'messaging': Messaging;\n  }\n}\n\nregisterMessagingInWindow();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAmBO,IAAM,qBAAqB;AAE3B,IAAM,kBAAkB,KAAK,OAAO;AACpC,IAAM,wBAAwB;AAE9B,IAAM,wBACX;AAEK,IAAM,0BAA0B,KAAK,KAAK;AAE1C,IAAM,UAAU;AAChB,IAAM,eAAe;ACD5B,IAAM,wBAAiE;EACrE;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GAA4B;EAC5B;IAAA;;EAAA,GAAoC;EACpC;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GAAyB;EACzB;IAAA;;EAAA,GACE;;AAaG,IAAM,gBAAgB,IAAI,aAC/B,SACA,cACA,qBAAqB;AAYjB,SAAU,cAAc,OAAc;AAC1C,SACE,iBAAiB,iBACjB,MAAM,KAAK;IAAQ;;EAAA;AAEvB;ACxCgB,SAAA,yBAAyB,EAAE,UAAS,GAAa;AAC/D,SAAO,GAAG,qBAAqB,aAAa,SAAS;AACvD;AAEM,SAAU,iCACd,UAAmC;AAEnC,SAAO;IACL,OAAO,SAAS;IAChB,eAAsC;IACtC,WAAW,kCAAkC,SAAS,SAAS;IAC/D,cAAc,KAAK,IAAG;;AAE1B;AAEO,eAAe,qBACpB,aACA,UAAkB;AAElB,QAAM,eAA8B,MAAM,SAAS,KAAI;AACvD,QAAM,YAAY,aAAa;AAC/B,SAAO,cAAc,OAAiC,kBAAA;IACpD;IACA,YAAY,UAAU;IACtB,eAAe,UAAU;IACzB,cAAc,UAAU;EACzB,CAAA;AACH;AAEgB,SAAA,WAAW,EAAE,OAAM,GAAa;AAC9C,SAAO,IAAI,QAAQ;IACjB,gBAAgB;IAChB,QAAQ;IACR,kBAAkB;EACnB,CAAA;AACH;SAEgB,mBACd,WACA,EAAE,aAAY,GAA+B;AAE7C,QAAM,UAAU,WAAW,SAAS;AACpC,UAAQ,OAAO,iBAAiB,uBAAuB,YAAY,CAAC;AACpE,SAAO;AACT;AAeO,eAAe,mBACpB,IAA2B;AAE3B,QAAM,SAAS,MAAM,GAAE;AAEvB,MAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;AAE/C,WAAO,GAAE;EACV;AAED,SAAO;AACT;AAEA,SAAS,kCAAkC,mBAAyB;AAElE,SAAO,OAAO,kBAAkB,QAAQ,KAAK,KAAK,CAAC;AACrD;AAEA,SAAS,uBAAuB,cAAoB;AAClD,SAAO,GAAG,qBAAqB,IAAI,YAAY;AACjD;AC7EO,eAAe,0BACpB,EAAE,WAAW,yBAAwB,GACrC,EAAE,IAAG,GAA+B;AAEpC,QAAM,WAAW,yBAAyB,SAAS;AAEnD,QAAM,UAAU,WAAW,SAAS;AAGpC,QAAM,mBAAmB,yBAAyB,aAAa;IAC7D,UAAU;EACX,CAAA;AACD,MAAI,kBAAkB;AACpB,UAAM,mBAAmB,MAAM,iBAAiB,oBAAmB;AACnE,QAAI,kBAAkB;AACpB,cAAQ,OAAO,qBAAqB,gBAAgB;IACrD;EACF;AAED,QAAM,OAAO;IACX;IACA,aAAa;IACb,OAAO,UAAU;IACjB,YAAY;;AAGd,QAAM,UAAuB;IAC3B,QAAQ;IACR;IACA,MAAM,KAAK,UAAU,IAAI;;AAG3B,QAAM,WAAW,MAAM,mBAAmB,MAAM,MAAM,UAAU,OAAO,CAAC;AACxE,MAAI,SAAS,IAAI;AACf,UAAM,gBAA4C,MAAM,SAAS,KAAI;AACrE,UAAM,8BAA2D;MAC/D,KAAK,cAAc,OAAO;MAC1B,oBAA2C;MAC3C,cAAc,cAAc;MAC5B,WAAW,iCAAiC,cAAc,SAAS;;AAErE,WAAO;EACR,OAAM;AACL,UAAM,MAAM,qBAAqB,uBAAuB,QAAQ;EACjE;AACH;AC5DM,SAAU,MAAM,IAAU;AAC9B,SAAO,IAAI,QAAc,aAAU;AACjC,eAAW,SAAS,EAAE;EACxB,CAAC;AACH;ACLM,SAAU,sBAAsB,OAAiB;AACrD,QAAM,MAAM,KAAK,OAAO,aAAa,GAAG,KAAK,CAAC;AAC9C,SAAO,IAAI,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AACnD;ACDO,IAAM,oBAAoB;AAC1B,IAAM,cAAc;SAMX,cAAW;AACzB,MAAI;AAGF,UAAM,eAAe,IAAI,WAAW,EAAE;AACtC,UAAM,SACJ,KAAK,UAAW,KAAyC;AAC3D,WAAO,gBAAgB,YAAY;AAGnC,iBAAa,CAAC,IAAI,MAAc,aAAa,CAAC,IAAI;AAElD,UAAM,MAAM,OAAO,YAAY;AAE/B,WAAO,kBAAkB,KAAK,GAAG,IAAI,MAAM;EAC5C,SAAO,IAAA;AAEN,WAAO;EACR;AACH;AAGA,SAAS,OAAO,cAAwB;AACtC,QAAM,YAAY,sBAAsB,YAAY;AAIpD,SAAO,UAAU,OAAO,GAAG,EAAE;AAC/B;AClCM,SAAU,OAAO,WAAoB;AACzC,SAAO,GAAG,UAAU,OAAO,IAAI,UAAU,KAAK;AAChD;ACDA,IAAM,qBAA2D,oBAAI,IAAG;AAMxD,SAAA,WAAW,WAAsB,KAAW;AAC1D,QAAM,MAAM,OAAO,SAAS;AAE5B,yBAAuB,KAAK,GAAG;AAC/B,qBAAmB,KAAK,GAAG;AAC7B;AAyCA,SAAS,uBAAuB,KAAa,KAAW;AACtD,QAAM,YAAY,mBAAmB,IAAI,GAAG;AAC5C,MAAI,CAAC,WAAW;AACd;EACD;AAED,aAAW,YAAY,WAAW;AAChC,aAAS,GAAG;EACb;AACH;AAEA,SAAS,mBAAmB,KAAa,KAAW;AAClD,QAAM,UAAU,oBAAmB;AACnC,MAAI,SAAS;AACX,YAAQ,YAAY,EAAE,KAAK,IAAG,CAAE;EACjC;AACD,wBAAqB;AACvB;AAEA,IAAI,mBAA4C;AAEhD,SAAS,sBAAmB;AAC1B,MAAI,CAAC,oBAAoB,sBAAsB,MAAM;AACnD,uBAAmB,IAAI,iBAAiB,uBAAuB;AAC/D,qBAAiB,YAAY,OAAI;AAC/B,6BAAuB,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG;IAC/C;EACD;AACD,SAAO;AACT;AAEA,SAAS,wBAAqB;AAC5B,MAAI,mBAAmB,SAAS,KAAK,kBAAkB;AACrD,qBAAiB,MAAK;AACtB,uBAAmB;EACpB;AACH;ACtFA,IAAM,gBAAgB;AACtB,IAAM,mBAAmB;AACzB,IAAM,oBAAoB;AAS1B,IAAI,YAA2D;AAC/D,SAAS,eAAY;AACnB,MAAI,CAAC,WAAW;AACd,gBAAY,OAAO,eAAe,kBAAkB;MAClD,SAAS,CAAC,IAAI,eAAc;AAM1B,gBAAQ,YAAU;UAChB,KAAK;AACH,eAAG,kBAAkB,iBAAiB;QACzC;;IAEJ,CAAA;EACF;AACD,SAAO;AACT;AAeO,eAAe,IACpB,WACA,OAAgB;AAEhB,QAAM,MAAM,OAAO,SAAS;AAC5B,QAAM,KAAK,MAAM,aAAY;AAC7B,QAAM,KAAK,GAAG,YAAY,mBAAmB,WAAW;AACxD,QAAM,cAAc,GAAG,YAAY,iBAAiB;AACpD,QAAM,WAAY,MAAM,YAAY,IAAI,GAAG;AAC3C,QAAM,YAAY,IAAI,OAAO,GAAG;AAChC,QAAM,GAAG;AAET,MAAI,CAAC,YAAY,SAAS,QAAQ,MAAM,KAAK;AAC3C,eAAW,WAAW,MAAM,GAAG;EAChC;AAED,SAAO;AACT;AAGO,eAAe,OAAO,WAAoB;AAC/C,QAAM,MAAM,OAAO,SAAS;AAC5B,QAAM,KAAK,MAAM,aAAY;AAC7B,QAAM,KAAK,GAAG,YAAY,mBAAmB,WAAW;AACxD,QAAM,GAAG,YAAY,iBAAiB,EAAE,OAAO,GAAG;AAClD,QAAM,GAAG;AACX;AAQO,eAAe,OACpB,WACA,UAAqE;AAErE,QAAM,MAAM,OAAO,SAAS;AAC5B,QAAM,KAAK,MAAM,aAAY;AAC7B,QAAM,KAAK,GAAG,YAAY,mBAAmB,WAAW;AACxD,QAAM,QAAQ,GAAG,YAAY,iBAAiB;AAC9C,QAAM,WAA2C,MAAM,MAAM,IAC3D,GAAG;AAEL,QAAM,WAAW,SAAS,QAAQ;AAElC,MAAI,aAAa,QAAW;AAC1B,UAAM,MAAM,OAAO,GAAG;EACvB,OAAM;AACL,UAAM,MAAM,IAAI,UAAU,GAAG;EAC9B;AACD,QAAM,GAAG;AAET,MAAI,aAAa,CAAC,YAAY,SAAS,QAAQ,SAAS,MAAM;AAC5D,eAAW,WAAW,SAAS,GAAG;EACnC;AAED,SAAO;AACT;AClFO,eAAe,qBACpB,eAAwC;AAExC,MAAI;AAEJ,QAAM,oBAAoB,MAAM,OAAO,cAAc,WAAW,cAAW;AACzE,UAAMA,qBAAoB,gCAAgC,QAAQ;AAClE,UAAM,mBAAmB,+BACvB,eACAA,kBAAiB;AAEnB,0BAAsB,iBAAiB;AACvC,WAAO,iBAAiB;EAC1B,CAAC;AAED,MAAI,kBAAkB,QAAQ,aAAa;AAEzC,WAAO,EAAE,mBAAmB,MAAM,oBAAoB;EACvD;AAED,SAAO;IACL;IACA;;AAEJ;AAMA,SAAS,gCACP,UAAuC;AAEvC,QAAM,QAA2B,YAAY;IAC3C,KAAK,YAAW;IAChB,oBAA6C;;;AAG/C,SAAO,qBAAqB,KAAK;AACnC;AASA,SAAS,+BACP,eACA,mBAAoC;AAEpC,MAAI,kBAAkB,uBAAkB,GAAgC;AACtE,QAAI,CAAC,UAAU,QAAQ;AAErB,YAAM,+BAA+B,QAAQ,OAC3C,cAAc;QAA6B;;MAAA,CAAA;AAE7C,aAAO;QACL;QACA,qBAAqB;;IAExB;AAGD,UAAM,kBAA+C;MACnD,KAAK,kBAAkB;MACvB,oBAA6C;MAC7C,kBAAkB,KAAK,IAAG;;AAE5B,UAAM,sBAAsB,qBAC1B,eACA,eAAe;AAEjB,WAAO,EAAE,mBAAmB,iBAAiB,oBAAmB;EACjE,WACC,kBAAkB,uBAAkB,GACpC;AACA,WAAO;MACL;MACA,qBAAqB,yBAAyB,aAAa;;EAE9D,OAAM;AACL,WAAO,EAAE,kBAAiB;EAC3B;AACH;AAGA,eAAe,qBACb,eACA,mBAA8C;AAE9C,MAAI;AACF,UAAM,8BAA8B,MAAM,0BACxC,eACA,iBAAiB;AAEnB,WAAO,IAAI,cAAc,WAAW,2BAA2B;EAChE,SAAQ,GAAG;AACV,QAAI,cAAc,CAAC,KAAK,EAAE,WAAW,eAAe,KAAK;AAGvD,YAAM,OAAO,cAAc,SAAS;IACrC,OAAM;AAEL,YAAM,IAAI,cAAc,WAAW;QACjC,KAAK,kBAAkB;QACvB,oBAA6C;;MAC9C,CAAA;IACF;AACD,UAAM;EACP;AACH;AAGA,eAAe,yBACb,eAAwC;AAMxC,MAAI,QAA2B,MAAM,0BACnC,cAAc,SAAS;AAEzB,SAAO,MAAM,uBAAkB,GAAgC;AAE7D,UAAM,MAAM,GAAG;AAEf,YAAQ,MAAM,0BAA0B,cAAc,SAAS;EAChE;AAED,MAAI,MAAM,uBAAkB,GAAgC;AAE1D,UAAM,EAAE,mBAAmB,oBAAmB,IAC5C,MAAM,qBAAqB,aAAa;AAE1C,QAAI,qBAAqB;AACvB,aAAO;IACR,OAAM;AAEL,aAAO;IACR;EACF;AAED,SAAO;AACT;AAUA,SAAS,0BACP,WAAoB;AAEpB,SAAO,OAAO,WAAW,cAAW;AAClC,QAAI,CAAC,UAAU;AACb,YAAM,cAAc;QAAM;;MAAA;IAC3B;AACD,WAAO,qBAAqB,QAAQ;EACtC,CAAC;AACH;AAEA,SAAS,qBAAqB,OAAwB;AACpD,MAAI,+BAA+B,KAAK,GAAG;AACzC,WAAO;MACL,KAAK,MAAM;MACX,oBAA6C;;;EAEhD;AAED,SAAO;AACT;AAEA,SAAS,+BACP,mBAAoC;AAEpC,SACE,kBAAkB,uBAAgD,KAClE,kBAAkB,mBAAmB,qBAAqB,KAAK,IAAG;AAEtE;AClMO,eAAe,yBACpB,EAAE,WAAW,yBAAwB,GACrC,mBAA8C;AAE9C,QAAM,WAAW,6BAA6B,WAAW,iBAAiB;AAE1E,QAAM,UAAU,mBAAmB,WAAW,iBAAiB;AAG/D,QAAM,mBAAmB,yBAAyB,aAAa;IAC7D,UAAU;EACX,CAAA;AACD,MAAI,kBAAkB;AACpB,UAAM,mBAAmB,MAAM,iBAAiB,oBAAmB;AACnE,QAAI,kBAAkB;AACpB,cAAQ,OAAO,qBAAqB,gBAAgB;IACrD;EACF;AAED,QAAM,OAAO;IACX,cAAc;MACZ,YAAY;MACZ,OAAO,UAAU;IAClB;;AAGH,QAAM,UAAuB;IAC3B,QAAQ;IACR;IACA,MAAM,KAAK,UAAU,IAAI;;AAG3B,QAAM,WAAW,MAAM,mBAAmB,MAAM,MAAM,UAAU,OAAO,CAAC;AACxE,MAAI,SAAS,IAAI;AACf,UAAM,gBAA2C,MAAM,SAAS,KAAI;AACpE,UAAM,qBACJ,iCAAiC,aAAa;AAChD,WAAO;EACR,OAAM;AACL,UAAM,MAAM,qBAAqB,uBAAuB,QAAQ;EACjE;AACH;AAEA,SAAS,6BACP,WACA,EAAE,IAAG,GAA+B;AAEpC,SAAO,GAAG,yBAAyB,SAAS,CAAC,IAAI,GAAG;AACtD;AC1CO,eAAe,iBACpB,eACA,eAAe,OAAK;AAEpB,MAAI;AACJ,QAAM,QAAQ,MAAM,OAAO,cAAc,WAAW,cAAW;AAC7D,QAAI,CAAC,kBAAkB,QAAQ,GAAG;AAChC,YAAM,cAAc;QAAM;;MAAA;IAC3B;AAED,UAAM,eAAe,SAAS;AAC9B,QAAI,CAAC,gBAAgB,iBAAiB,YAAY,GAAG;AAEnD,aAAO;IACR,WAAU,aAAa,kBAAa,GAAgC;AAEnE,qBAAe,0BAA0B,eAAe,YAAY;AACpE,aAAO;IACR,OAAM;AAEL,UAAI,CAAC,UAAU,QAAQ;AACrB,cAAM,cAAc;UAAM;;QAAA;MAC3B;AAED,YAAM,kBAAkB,oCAAoC,QAAQ;AACpE,qBAAe,yBAAyB,eAAe,eAAe;AACtE,aAAO;IACR;EACH,CAAC;AAED,QAAM,YAAY,eACd,MAAM,eACL,MAAM;AACX,SAAO;AACT;AAQA,eAAe,0BACb,eACA,cAAqB;AAMrB,MAAI,QAAQ,MAAM,uBAAuB,cAAc,SAAS;AAChE,SAAO,MAAM,UAAU,kBAAa,GAAgC;AAElE,UAAM,MAAM,GAAG;AAEf,YAAQ,MAAM,uBAAuB,cAAc,SAAS;EAC7D;AAED,QAAM,YAAY,MAAM;AACxB,MAAI,UAAU,kBAAa,GAAgC;AAEzD,WAAO,iBAAiB,eAAe,YAAY;EACpD,OAAM;AACL,WAAO;EACR;AACH;AAUA,SAAS,uBACP,WAAoB;AAEpB,SAAO,OAAO,WAAW,cAAW;AAClC,QAAI,CAAC,kBAAkB,QAAQ,GAAG;AAChC,YAAM,cAAc;QAAM;;MAAA;IAC3B;AAED,UAAM,eAAe,SAAS;AAC9B,QAAI,4BAA4B,YAAY,GAAG;AAC7C,aACK,OAAA,OAAA,OAAA,OAAA,CAAA,GAAA,QAAQ,GAAA,EACX,WAAW;QAAE,eAAa;;MAAA,EAA6B,CACvD;IACH;AAED,WAAO;EACT,CAAC;AACH;AAEA,eAAe,yBACb,eACA,mBAA8C;AAE9C,MAAI;AACF,UAAM,YAAY,MAAM,yBACtB,eACA,iBAAiB;AAEnB,UAAM,2BACD,OAAA,OAAA,OAAA,OAAA,CAAA,GAAA,iBAAiB,GACpB,EAAA,UAAS,CAAA;AAEX,UAAM,IAAI,cAAc,WAAW,wBAAwB;AAC3D,WAAO;EACR,SAAQ,GAAG;AACV,QACE,cAAc,CAAC,MACd,EAAE,WAAW,eAAe,OAAO,EAAE,WAAW,eAAe,MAChE;AAGA,YAAM,OAAO,cAAc,SAAS;IACrC,OAAM;AACL,YAAM,2BACD,OAAA,OAAA,OAAA,OAAA,CAAA,GAAA,iBAAiB,GACpB,EAAA,WAAW;QAAE,eAAa;;MAAA,EAA6B,CAAA;AAEzD,YAAM,IAAI,cAAc,WAAW,wBAAwB;IAC5D;AACD,UAAM;EACP;AACH;AAEA,SAAS,kBACP,mBAAgD;AAEhD,SACE,sBAAsB,UACtB,kBAAkB,uBAA8C;AAEpE;AAEA,SAAS,iBAAiB,WAAoB;AAC5C,SACE,UAAU,kBAAyC,KACnD,CAAC,mBAAmB,SAAS;AAEjC;AAEA,SAAS,mBAAmB,WAA6B;AACvD,QAAM,MAAM,KAAK,IAAG;AACpB,SACE,MAAM,UAAU,gBAChB,UAAU,eAAe,UAAU,YAAY,MAAM;AAEzD;AAGA,SAAS,oCACP,UAAqC;AAErC,QAAM,sBAA2C;IAC/C,eAAwC;IACxC,aAAa,KAAK,IAAG;;AAEvB,SAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,QAAQ,GAAA,EACX,WAAW,oBAAmB,CAC9B;AACJ;AAEA,SAAS,4BAA4B,WAAoB;AACvD,SACE,UAAU,kBAA2C,KACrD,UAAU,cAAc,qBAAqB,KAAK,IAAG;AAEzD;ACxLO,eAAe,MAAM,eAA4B;AACtD,QAAM,oBAAoB;AAC1B,QAAM,EAAE,mBAAmB,oBAAmB,IAAK,MAAM,qBACvD,iBAAiB;AAGnB,MAAI,qBAAqB;AACvB,wBAAoB,MAAM,QAAQ,KAAK;EACxC,OAAM;AAGL,qBAAiB,iBAAiB,EAAE,MAAM,QAAQ,KAAK;EACxD;AAED,SAAO,kBAAkB;AAC3B;ACdO,eAAe,SACpB,eACA,eAAe,OAAK;AAEpB,QAAM,oBAAoB;AAC1B,QAAM,iCAAiC,iBAAiB;AAIxD,QAAM,YAAY,MAAM,iBAAiB,mBAAmB,YAAY;AACxE,SAAO,UAAU;AACnB;AAEA,eAAe,iCACb,eAAwC;AAExC,QAAM,EAAE,oBAAmB,IAAK,MAAM,qBAAqB,aAAa;AAExE,MAAI,qBAAqB;AAEvB,UAAM;EACP;AACH;AK9BM,SAAU,iBAAiB,KAAgB;AAC/C,MAAI,CAAC,OAAO,CAAC,IAAI,SAAS;AACxB,UAAM,qBAAqB,mBAAmB;EAC/C;AAED,MAAI,CAAC,IAAI,MAAM;AACb,UAAM,qBAAqB,UAAU;EACtC;AAGD,QAAM,aAA2C;IAC/C;IACA;IACA;;AAGF,aAAW,WAAW,YAAY;AAChC,QAAI,CAAC,IAAI,QAAQ,OAAO,GAAG;AACzB,YAAM,qBAAqB,OAAO;IACnC;EACF;AAED,SAAO;IACL,SAAS,IAAI;IACb,WAAW,IAAI,QAAQ;IACvB,QAAQ,IAAI,QAAQ;IACpB,OAAO,IAAI,QAAQ;;AAEvB;AAEA,SAAS,qBAAqB,WAAiB;AAC7C,SAAO,cAAc,OAA4C,6BAAA;IAC/D;EACD,CAAA;AACH;AC3BA,IAAM,qBAAqB;AAC3B,IAAM,8BAA8B;AAEpC,IAAM,gBAAkD,CACtD,cACE;AACF,QAAM,MAAM,UAAU,YAAY,KAAK,EAAE,aAAY;AAErD,QAAM,YAAY,iBAAiB,GAAG;AACtC,QAAM,2BAA2B,aAAa,KAAK,WAAW;AAE9D,QAAM,oBAA+C;IACnD;IACA;IACA;IACA,SAAS,MAAM,QAAQ,QAAO;;AAEhC,SAAO;AACT;AAEA,IAAM,kBAA6D,CACjE,cACE;AACF,QAAM,MAAM,UAAU,YAAY,KAAK,EAAE,aAAY;AAErD,QAAM,gBAAgB,aAAa,KAAK,kBAAkB,EAAE,aAAY;AAExE,QAAM,wBAAwD;IAC5D,OAAO,MAAM,MAAM,aAAa;IAChC,UAAU,CAAC,iBAA2B,SAAS,eAAe,YAAY;;AAE5E,SAAO;AACT;SAEgB,wBAAqB;AACnC,qBACE,IAAI;IAAU;IAAoB;IAAoC;;EAAA,CAAA;AAExE,qBACE,IAAI;IACF;IACA;IAED;;EAAA,CAAA;AAEL;AC3CA,sBAAqB;AACrB,gBAAgB,MAAM,OAAO;AAE7B,gBAAgB,MAAM,SAAS,SAAkB;;;ACjB1C,IAAM,kBAAkB;AACxB,IAAM,mBAAmB;AAEzB,IAAM,oBACX;AAEK,IAAM,WAAW;AAKjB,IAAM,sBAAsB;AAC5B,IAAM,wBAAwB;AAC9B,IAAM,wBAAwB;AAE9B,IAAM,qCAAqC;AAelD,IAAYC;CAAZ,SAAYA,cAAW;AACrB,EAAAA,aAAAA,aAAA,cAAA,IAAA,CAAA,IAAA;AACA,EAAAA,aAAAA,aAAA,sBAAA,IAAA,CAAA,IAAA;AACF,GAHYA,kBAAAA,gBAGX,CAAA,EAAA;ACKD,IAAY;CAAZ,SAAYA,cAAW;AACrB,EAAAA,aAAA,eAAA,IAAA;AACA,EAAAA,aAAA,sBAAA,IAAA;AACF,GAHY,gBAAA,cAGX,CAAA,EAAA;ACzCK,SAAU,cAAc,OAA+B;AAC3D,QAAM,aAAa,IAAI,WAAW,KAAK;AACvC,QAAM,eAAe,KAAK,OAAO,aAAa,GAAG,UAAU,CAAC;AAC5D,SAAO,aAAa,QAAQ,MAAM,EAAE,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AAC9E;AAEM,SAAU,cAAc,cAAoB;AAChD,QAAM,UAAU,IAAI,QAAQ,IAAK,aAAa,SAAS,KAAM,CAAC;AAC9D,QAAM,UAAU,eAAe,SAC5B,QAAQ,OAAO,GAAG,EAClB,QAAQ,MAAM,GAAG;AAEpB,QAAM,UAAU,KAAK,MAAM;AAC3B,QAAM,cAAc,IAAI,WAAW,QAAQ,MAAM;AAEjD,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACvC,gBAAY,CAAC,IAAI,QAAQ,WAAW,CAAC;EACtC;AACD,SAAO;AACT;ACyBA,IAAM,cAAc;AAKpB,IAAM,iBAAiB;AACvB,IAAM,wBAAwB;AAEvB,eAAe,mBACpB,UAAgB;AAEhB,MAAI,eAAe,WAAW;AAG5B,UAAM,YAAY,MAChB,UAGA,UAAS;AACX,UAAM,UAAU,UAAU,IAAI,CAAAC,QAAMA,IAAG,IAAI;AAE3C,QAAI,CAAC,QAAQ,SAAS,WAAW,GAAG;AAElC,aAAO;IACR;EACF;AAED,MAAI,eAAoC;AAExC,QAAM,KAAK,MAAM,OAAO,aAAa,gBAAgB;IACnD,SAAS,OAAOA,KAAI,YAAY,YAAY,uBAAsB;;AAChE,UAAI,aAAa,GAAG;AAElB;MACD;AAED,UAAI,CAACA,IAAG,iBAAiB,SAAS,qBAAqB,GAAG;AAExD;MACD;AAED,YAAM,cAAc,mBAAmB,YAAY,qBAAqB;AACxE,YAAM,QAAQ,MAAM,YAAY,MAAM,aAAa,EAAE,IAAI,QAAQ;AACjE,YAAM,YAAY,MAAK;AAEvB,UAAI,CAAC,OAAO;AAEV;MACD;AAED,UAAI,eAAe,GAAG;AACpB,cAAM,aAAa;AAEnB,YAAI,CAAC,WAAW,QAAQ,CAAC,WAAW,UAAU,CAAC,WAAW,UAAU;AAClE;QACD;AAED,uBAAe;UACb,OAAO,WAAW;UAClB,aAAY,KAAA,WAAW,gBAAc,QAAA,OAAA,SAAA,KAAA,KAAK,IAAG;UAC7C,qBAAqB;YACnB,MAAM,WAAW;YACjB,QAAQ,WAAW;YACnB,UAAU,WAAW;YACrB,SAAS,WAAW;YACpB,UACE,OAAO,WAAW,aAAa,WAC3B,WAAW,WACX,cAAc,WAAW,QAAQ;UACxC;;MAEJ,WAAU,eAAe,GAAG;AAC3B,cAAM,aAAa;AAEnB,uBAAe;UACb,OAAO,WAAW;UAClB,YAAY,WAAW;UACvB,qBAAqB;YACnB,MAAM,cAAc,WAAW,IAAI;YACnC,QAAQ,cAAc,WAAW,MAAM;YACvC,UAAU,WAAW;YACrB,SAAS,WAAW;YACpB,UAAU,cAAc,WAAW,QAAQ;UAC5C;;MAEJ,WAAU,eAAe,GAAG;AAC3B,cAAM,aAAa;AAEnB,uBAAe;UACb,OAAO,WAAW;UAClB,YAAY,WAAW;UACvB,qBAAqB;YACnB,MAAM,cAAc,WAAW,IAAI;YACnC,QAAQ,cAAc,WAAW,MAAM;YACvC,UAAU,WAAW;YACrB,SAAS,WAAW;YACpB,UAAU,cAAc,WAAW,QAAQ;UAC5C;;MAEJ;;EAEJ,CAAA;AACD,KAAG,MAAK;AAGR,QAAM,SAAS,WAAW;AAC1B,QAAM,SAAS,sBAAsB;AACrC,QAAM,SAAS,WAAW;AAE1B,SAAO,kBAAkB,YAAY,IAAI,eAAe;AAC1D;AAEA,SAAS,kBACP,cAAiC;AAEjC,MAAI,CAAC,gBAAgB,CAAC,aAAa,qBAAqB;AACtD,WAAO;EACR;AACD,QAAM,EAAE,oBAAmB,IAAK;AAChC,SACE,OAAO,aAAa,eAAe,YACnC,aAAa,aAAa,KAC1B,OAAO,aAAa,UAAU,YAC9B,aAAa,MAAM,SAAS,KAC5B,OAAO,oBAAoB,SAAS,YACpC,oBAAoB,KAAK,SAAS,KAClC,OAAO,oBAAoB,WAAW,YACtC,oBAAoB,OAAO,SAAS,KACpC,OAAO,oBAAoB,aAAa,YACxC,oBAAoB,SAAS,SAAS,KACtC,OAAO,oBAAoB,YAAY,YACvC,oBAAoB,QAAQ,SAAS,KACrC,OAAO,oBAAoB,aAAa,YACxC,oBAAoB,SAAS,SAAS;AAE1C;AC5KO,IAAMC,iBAAgB;AAC7B,IAAMC,oBAAmB;AACzB,IAAMC,qBAAoB;AAS1B,IAAIC,aAAuD;AAC3D,SAASC,gBAAY;AACnB,MAAI,CAACD,YAAW;AACd,IAAAA,aAAY,OAAOH,gBAAeC,mBAAkB;MAClD,SAAS,CAAC,WAAW,eAAc;AAKjC,gBAAQ,YAAU;UAChB,KAAK;AACH,sBAAU,kBAAkBC,kBAAiB;QAChD;;IAEJ,CAAA;EACF;AACD,SAAOC;AACT;AAGO,eAAe,MACpB,sBAAkD;AAElD,QAAM,MAAME,QAAO,oBAAoB;AACvC,QAAM,KAAK,MAAMD,cAAY;AAC7B,QAAM,eAAgB,MAAM,GACzB,YAAYF,kBAAiB,EAC7B,YAAYA,kBAAiB,EAC7B,IAAI,GAAG;AAEV,MAAI,cAAc;AAChB,WAAO;EACR,OAAM;AAEL,UAAM,kBAAkB,MAAM,mBAC5B,qBAAqB,UAAU,QAAQ;AAEzC,QAAI,iBAAiB;AACnB,YAAM,MAAM,sBAAsB,eAAe;AACjD,aAAO;IACR;EACF;AACH;AAGO,eAAe,MACpB,sBACA,cAA0B;AAE1B,QAAM,MAAMG,QAAO,oBAAoB;AACvC,QAAM,KAAK,MAAMD,cAAY;AAC7B,QAAM,KAAK,GAAG,YAAYF,oBAAmB,WAAW;AACxD,QAAM,GAAG,YAAYA,kBAAiB,EAAE,IAAI,cAAc,GAAG;AAC7D,QAAM,GAAG;AACT,SAAO;AACT;AAGO,eAAe,SACpB,sBAAkD;AAElD,QAAM,MAAMG,QAAO,oBAAoB;AACvC,QAAM,KAAK,MAAMD,cAAY;AAC7B,QAAM,KAAK,GAAG,YAAYF,oBAAmB,WAAW;AACxD,QAAM,GAAG,YAAYA,kBAAiB,EAAE,OAAO,GAAG;AAClD,QAAM,GAAG;AACX;AAWA,SAASG,QAAO,EAAE,UAAS,GAAgC;AACzD,SAAO,UAAU;AACnB;AC1EO,IAAM,YAAiC;EAC5C;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EAEF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EAEF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GAA+B;EAC/B;IAAA;;EAAA,GACE;;AAcG,IAAMC,iBAAgB,IAAI,aAC/B,aACA,aACA,SAAS;ACxDJ,eAAe,gBACpB,sBACA,qBAAwC;AAExC,QAAM,UAAU,MAAMC,YAAW,oBAAoB;AACrD,QAAM,OAAO,QAAQ,mBAAmB;AAExC,QAAM,mBAAmB;IACvB,QAAQ;IACR;IACA,MAAM,KAAK,UAAU,IAAI;;AAG3B,MAAI;AACJ,MAAI;AACF,UAAM,WAAW,MAAM,MACrB,YAAY,qBAAqB,SAAS,GAC1C,gBAAgB;AAElB,mBAAe,MAAM,SAAS,KAAI;EACnC,SAAQ,KAAK;AACZ,UAAMD,eAAc,OAAyC,0BAAA;MAC3D,WAAY,QAAA,QAAA,QAAG,SAAA,SAAH,IAAe,SAAQ;IACpC,CAAA;EACF;AAED,MAAI,aAAa,OAAO;AACtB,UAAM,UAAU,aAAa,MAAM;AACnC,UAAMA,eAAc,OAAyC,0BAAA;MAC3D,WAAW;IACZ,CAAA;EACF;AAED,MAAI,CAAC,aAAa,OAAO;AACvB,UAAMA,eAAc;MAAM;;IAAA;EAC3B;AAED,SAAO,aAAa;AACtB;AAEO,eAAe,mBACpB,sBACA,cAA0B;AAE1B,QAAM,UAAU,MAAMC,YAAW,oBAAoB;AACrD,QAAM,OAAO,QAAQ,aAAa,mBAAoB;AAEtD,QAAM,gBAAgB;IACpB,QAAQ;IACR;IACA,MAAM,KAAK,UAAU,IAAI;;AAG3B,MAAI;AACJ,MAAI;AACF,UAAM,WAAW,MAAM,MACrB,GAAG,YAAY,qBAAqB,SAAS,CAAC,IAAI,aAAa,KAAK,IACpE,aAAa;AAEf,mBAAe,MAAM,SAAS,KAAI;EACnC,SAAQ,KAAK;AACZ,UAAMD,eAAc,OAAsC,uBAAA;MACxD,WAAY,QAAA,QAAA,QAAG,SAAA,SAAH,IAAe,SAAQ;IACpC,CAAA;EACF;AAED,MAAI,aAAa,OAAO;AACtB,UAAM,UAAU,aAAa,MAAM;AACnC,UAAMA,eAAc,OAAsC,uBAAA;MACxD,WAAW;IACZ,CAAA;EACF;AAED,MAAI,CAAC,aAAa,OAAO;AACvB,UAAMA,eAAc;MAAM;;IAAA;EAC3B;AAED,SAAO,aAAa;AACtB;AAEO,eAAe,mBACpB,sBACA,OAAa;AAEb,QAAM,UAAU,MAAMC,YAAW,oBAAoB;AAErD,QAAM,qBAAqB;IACzB,QAAQ;IACR;;AAGF,MAAI;AACF,UAAM,WAAW,MAAM,MACrB,GAAG,YAAY,qBAAqB,SAAS,CAAC,IAAI,KAAK,IACvD,kBAAkB;AAEpB,UAAM,eAA4B,MAAM,SAAS,KAAI;AACrD,QAAI,aAAa,OAAO;AACtB,YAAM,UAAU,aAAa,MAAM;AACnC,YAAMD,eAAc,OAA2C,4BAAA;QAC7D,WAAW;MACZ,CAAA;IACF;EACF,SAAQ,KAAK;AACZ,UAAMA,eAAc,OAA2C,4BAAA;MAC7D,WAAY,QAAA,QAAA,QAAG,SAAA,SAAH,IAAe,SAAQ;IACpC,CAAA;EACF;AACH;AAEA,SAAS,YAAY,EAAE,UAAS,GAAa;AAC3C,SAAO,GAAG,QAAQ,aAAa,SAAU;AAC3C;AAEA,eAAeC,YAAW,EACxB,WACA,cAAa,GACgB;AAC7B,QAAM,YAAY,MAAM,cAAc,SAAQ;AAE9C,SAAO,IAAI,QAAQ;IACjB,gBAAgB;IAChB,QAAQ;IACR,kBAAkB,UAAU;IAC5B,sCAAsC,OAAO,SAAS;EACvD,CAAA;AACH;AAEA,SAAS,QAAQ,EACf,QACA,MACA,UACA,SAAQ,GACY;AACpB,QAAM,OAAuB;IAC3B,KAAK;MACH;MACA;MACA;IACD;;AAGH,MAAI,aAAa,mBAAmB;AAClC,SAAK,IAAI,oBAAoB;EAC9B;AAED,SAAO;AACT;ACxJA,IAAM,sBAAsB,IAAI,KAAK,KAAK,KAAK;AAExC,eAAe,iBACpB,WAA2B;AAE3B,QAAM,mBAAmB,MAAM,oBAC7B,UAAU,gBACV,UAAU,QAAS;AAGrB,QAAM,sBAA2C;IAC/C,UAAU,UAAU;IACpB,SAAS,UAAU,eAAgB;IACnC,UAAU,iBAAiB;IAC3B,MAAM,cAAc,iBAAiB,OAAO,MAAM,CAAE;IACpD,QAAQ,cAAc,iBAAiB,OAAO,QAAQ,CAAE;;AAG1D,QAAM,eAAe,MAAM,MAAM,UAAU,oBAAoB;AAC/D,MAAI,CAAC,cAAc;AAEjB,WAAO,YAAY,UAAU,sBAAsB,mBAAmB;EACvE,WACC,CAAC,aAAa,aAAa,qBAAsB,mBAAmB,GACpE;AAEA,QAAI;AACF,YAAM,mBACJ,UAAU,sBACV,aAAa,KAAK;IAErB,SAAQ,GAAG;AAEV,cAAQ,KAAK,CAAC;IACf;AAED,WAAO,YAAY,UAAU,sBAAuB,mBAAmB;EACxE,WAAU,KAAK,IAAG,KAAM,aAAa,aAAa,qBAAqB;AAEtE,WAAO,YAAY,WAAW;MAC5B,OAAO,aAAa;MACpB,YAAY,KAAK,IAAG;MACpB;IACD,CAAA;EACF,OAAM;AAEL,WAAO,aAAa;EACrB;AACH;AAMO,eAAe,oBACpB,WAA2B;AAE3B,QAAM,eAAe,MAAM,MAAM,UAAU,oBAAoB;AAC/D,MAAI,cAAc;AAChB,UAAM,mBACJ,UAAU,sBACV,aAAa,KAAK;AAEpB,UAAM,SAAS,UAAU,oBAAoB;EAC9C;AAGD,QAAM,mBACJ,MAAM,UAAU,eAAgB,YAAY,gBAAe;AAC7D,MAAI,kBAAkB;AACpB,WAAO,iBAAiB,YAAW;EACpC;AAGD,SAAO;AACT;AAEA,eAAe,YACb,WACA,cAA0B;AAE1B,MAAI;AACF,UAAM,eAAe,MAAM,mBACzB,UAAU,sBACV,YAAY;AAGd,UAAM,sBAAmB,OAAA,OAAA,OAAA,OAAA,CAAA,GACpB,YAAY,GAAA,EACf,OAAO,cACP,YAAY,KAAK,IAAG,EAAE,CAAA;AAGxB,UAAM,MAAM,UAAU,sBAAsB,mBAAmB;AAC/D,WAAO;EACR,SAAQ,GAAG;AACV,UAAM;EACP;AACH;AAEA,eAAe,YACb,sBACA,qBAAwC;AAExC,QAAM,QAAQ,MAAM,gBAClB,sBACA,mBAAmB;AAErB,QAAM,eAA6B;IACjC;IACA,YAAY,KAAK,IAAG;IACpB;;AAEF,QAAM,MAAM,sBAAsB,YAAY;AAC9C,SAAO,aAAa;AACtB;AAKA,eAAe,oBACb,gBACA,UAAgB;AAEhB,QAAM,eAAe,MAAM,eAAe,YAAY,gBAAe;AACrE,MAAI,cAAc;AAChB,WAAO;EACR;AAED,SAAO,eAAe,YAAY,UAAU;IAC1C,iBAAiB;;;IAGjB,sBAAsB,cAAc,QAAQ;EAC7C,CAAA;AACH;AAKA,SAAS,aACP,WACA,gBAAmC;AAEnC,QAAM,kBAAkB,eAAe,aAAa,UAAU;AAC9D,QAAM,kBAAkB,eAAe,aAAa,UAAU;AAC9D,QAAM,cAAc,eAAe,SAAS,UAAU;AACtD,QAAM,gBAAgB,eAAe,WAAW,UAAU;AAE1D,SAAO,mBAAmB,mBAAmB,eAAe;AAC9D;ACnKM,SAAU,mBACd,iBAAuC;AAEvC,QAAM,UAA0B;IAC9B,MAAM,gBAAgB;;IAEtB,aAAa,gBAAgB;;IAE7B,WAAW,gBAAgB;;AAG7B,+BAA6B,SAAS,eAAe;AACrD,uBAAqB,SAAS,eAAe;AAC7C,sBAAoB,SAAS,eAAe;AAE5C,SAAO;AACT;AAEA,SAAS,6BACP,SACA,wBAA8C;AAE9C,MAAI,CAAC,uBAAuB,cAAc;AACxC;EACD;AAED,UAAQ,eAAe,CAAA;AAEvB,QAAM,QAAQ,uBAAuB,aAAc;AACnD,MAAI,CAAC,CAAC,OAAO;AACX,YAAQ,aAAc,QAAQ;EAC/B;AAED,QAAM,OAAO,uBAAuB,aAAc;AAClD,MAAI,CAAC,CAAC,MAAM;AACV,YAAQ,aAAc,OAAO;EAC9B;AAED,QAAM,QAAQ,uBAAuB,aAAc;AACnD,MAAI,CAAC,CAAC,OAAO;AACX,YAAQ,aAAc,QAAQ;EAC/B;AAED,QAAM,OAAO,uBAAuB,aAAc;AAClD,MAAI,CAAC,CAAC,MAAM;AACV,YAAQ,aAAc,OAAO;EAC9B;AACH;AAEA,SAAS,qBACP,SACA,wBAA8C;AAE9C,MAAI,CAAC,uBAAuB,MAAM;AAChC;EACD;AAED,UAAQ,OAAO,uBAAuB;AACxC;AAEA,SAAS,oBACP,SACA,wBAA8C;;AAG9C,MACE,CAAC,uBAAuB,cACxB,GAAC,KAAA,uBAAuB,kBAAc,QAAA,OAAA,SAAA,SAAA,GAAA,eACtC;AACA;EACD;AAED,UAAQ,aAAa,CAAA;AAErB,QAAM,QACJ,MAAA,KAAA,uBAAuB,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,UAAI,QAAA,OAAA,SAAA,MACvC,KAAA,uBAAuB,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE;AAEvC,MAAI,CAAC,CAAC,MAAM;AACV,YAAQ,WAAY,OAAO;EAC5B;AAGD,QAAM,kBAAiB,KAAA,uBAAuB,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE;AAC1D,MAAI,CAAC,CAAC,gBAAgB;AACpB,YAAQ,WAAY,iBAAiB;EACtC;AACH;ACvFM,SAAU,iBAAiB,MAAa;AAE5C,SAAO,OAAO,SAAS,YAAY,CAAC,CAAC,QAAQ,uBAAuB;AACtE;ACiB0B,cACxB,wBACA,qBAAqB;AAgNP,SAAA,cAAc,IAAY,IAAU;AAClD,QAAM,cAAc,CAAA;AACpB,WAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,gBAAY,KAAK,GAAG,OAAO,CAAC,CAAC;AAC7B,QAAI,IAAI,GAAG,QAAQ;AACjB,kBAAY,KAAK,GAAG,OAAO,CAAC,CAAC;IAC9B;EACF;AAED,SAAO,YAAY,KAAK,EAAE;AAC5B;AC7OM,SAAUC,kBAAiB,KAAgB;AAC/C,MAAI,CAAC,OAAO,CAAC,IAAI,SAAS;AACxB,UAAMC,sBAAqB,0BAA0B;EACtD;AAED,MAAI,CAAC,IAAI,MAAM;AACb,UAAMA,sBAAqB,UAAU;EACtC;AAGD,QAAM,aAAmD;IACvD;IACA;IACA;IACA;;AAGF,QAAM,EAAE,QAAO,IAAK;AACpB,aAAW,WAAW,YAAY;AAChC,QAAI,CAAC,QAAQ,OAAO,GAAG;AACrB,YAAMA,sBAAqB,OAAO;IACnC;EACF;AAED,SAAO;IACL,SAAS,IAAI;IACb,WAAW,QAAQ;IACnB,QAAQ,QAAQ;IAChB,OAAO,QAAQ;IACf,UAAU,QAAQ;;AAEtB;AAEA,SAASA,sBAAqB,WAAiB;AAC7C,SAAOH,eAAc,OAA4C,6BAAA;IAC/D;EACD,CAAA;AACH;ICjCa,yBAAgB;EAoB3B,YACE,KACA,eACA,mBAA0D;AAhB5D,SAAwC,2CAAY;AAEpD,SAA0B,6BAGf;AAEX,SAAgB,mBACd;AAEF,SAAS,YAAe,CAAA;AACxB,SAAmB,sBAAY;AAO7B,UAAM,YAAYE,kBAAiB,GAAG;AAEtC,SAAK,uBAAuB;MAC1B;MACA;MACA;MACA;;;EAIJ,UAAO;AACL,WAAO,QAAQ,QAAO;;AAEzB;AC3CM,eAAe,kBACpB,WAA2B;AAE3B,MAAI;AACF,cAAU,iBAAiB,MAAM,UAAU,cAAc,SACvD,iBACA;MACE,OAAO;IACR,CAAA;AAQH,cAAU,eAAe,OAAM,EAAG,MAAM,MAAK;IAE7C,CAAC;EACF,SAAQ,GAAG;AACV,UAAMF,eAAc,OAA8C,sCAAA;MAChE,qBAAsB,MAAW,QAAX,MAAA,SAAA,SAAA,EAAa;IACpC,CAAA;EACF;AACH;ACxBO,eAAe,YACpB,WACA,gBAAsD;AAEtD,MAAI,CAAC,kBAAkB,CAAC,UAAU,gBAAgB;AAChD,UAAM,kBAAkB,SAAS;EAClC;AAED,MAAI,CAAC,kBAAkB,CAAC,CAAC,UAAU,gBAAgB;AACjD;EACD;AAED,MAAI,EAAE,0BAA0B,4BAA4B;AAC1D,UAAMA,eAAc;MAAM;;IAAA;EAC3B;AAED,YAAU,iBAAiB;AAC7B;ACnBO,eAAe,eACpB,WACA,UAA6B;AAE7B,MAAI,CAAC,CAAC,UAAU;AACd,cAAU,WAAW;EACtB,WAAU,CAAC,UAAU,UAAU;AAC9B,cAAU,WAAW;EACtB;AACH;ACJO,eAAeI,WACpB,WACA,SAAyB;AAEzB,MAAI,CAAC,WAAW;AACd,UAAMJ,eAAc;MAAM;;IAAA;EAC3B;AAED,MAAI,aAAa,eAAe,WAAW;AACzC,UAAM,aAAa,kBAAiB;EACrC;AAED,MAAI,aAAa,eAAe,WAAW;AACzC,UAAMA,eAAc;MAAM;;IAAA;EAC3B;AAED,QAAM,eAAe,WAAW,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,QAAQ;AACjD,QAAM,YAAY,WAAW,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,yBAAyB;AAE/D,SAAO,iBAAiB,SAAS;AACnC;AChBO,eAAe,WACpB,WACA,aACA,MAAwB;AAExB,QAAM,YAAY,aAAa,WAAW;AAC1C,QAAM,YACJ,MAAM,UAAU,qBAAqB,kBAAkB,IAAG;AAC5D,YAAU,SAAS,WAAW;;IAE5B,YAAY,KAAK,mBAAmB;IACpC,cAAc,KAAK,qBAAqB;IACxC,cAAc,KAAK,qBAAqB;IACxC,qBAAqB,KAAK,MAAM,KAAK,IAAG,IAAK,GAAI;;EAElD,CAAA;AACH;AAEA,SAAS,aAAa,aAAwB;AAC5C,UAAQ,aAAW;IACjB,KAAK,YAAY;AACf,aAAO;IACT,KAAK,YAAY;AACf,aAAO;IACT;AACE,YAAM,IAAI,MAAK;EAClB;AACH;AC5BO,eAAe,qBACpB,WACA,OAAmB;AAEnB,QAAM,kBAAkB,MAAM;AAE9B,MAAI,CAAC,gBAAgB,qBAAqB;AACxC;EACD;AAED,MACE,UAAU,oBACV,gBAAgB,gBAAgB,YAAY,eAC5C;AACA,QAAI,OAAO,UAAU,qBAAqB,YAAY;AACpD,gBAAU,iBAAiB,mBAAmB,eAAe,CAAC;IAC/D,OAAM;AACL,gBAAU,iBAAiB,KAAK,mBAAmB,eAAe,CAAC;IACpE;EACF;AAGD,QAAM,cAAc,gBAAgB;AACpC,MACE,iBAAiB,WAAW,KAC5B,YAAY,kCAAkC,MAAM,KACpD;AACA,UAAM,WAAW,WAAW,gBAAgB,aAAc,WAAW;EACtE;AACH;;;AClBA,IAAM,yBAAuD,CAC3D,cACE;AACF,QAAM,YAAY,IAAI,iBACpB,UAAU,YAAY,KAAK,EAAE,aAAY,GACzC,UAAU,YAAY,wBAAwB,EAAE,aAAY,GAC5D,UAAU,YAAY,oBAAoB,CAAC;AAG7C,YAAU,cAAc,iBAAiB,WAAW,OAClD,qBAAqB,WAA+B,CAAC,CAAC;AAGxD,SAAO;AACT;AAEA,IAAM,iCAAwE,CAC5E,cACE;AACF,QAAM,YAAY,UACf,YAAY,WAAW,EACvB,aAAY;AAEf,QAAM,oBAAuC;IAC3C,UAAU,CAAC,YAA8BI,WAAS,WAAW,OAAO;;AAGtE,SAAO;AACT;SAyBgB,4BAAyB;AACvC,qBACE,IAAI;IAAU;IAAa;IAA6C;;EAAA,CAAA;AAG1E,qBACE,IAAI;IACF;IACA;IAED;;EAAA,CAAA;AAGH,kBAAgBC,OAAMC,QAAO;AAE7B,kBAAgBD,OAAMC,UAAS,SAAkB;AACnD;AC/EO,eAAe,oBAAiB;AACrC,MAAI;AAGF,UAAM,0BAAyB;EAChC,SAAQ,GAAG;AACV,WAAO;EACR;AAID,SACE,OAAO,WAAW,eAClB,qBAAoB,KACpB,kBAAiB,KACjB,mBAAmB,aACnB,iBAAiB,UACjB,kBAAkB,UAClB,WAAW,UACX,0BAA0B,UAAU,eAAe,kBAAkB,KACrE,iBAAiB,UAAU,eAAe,QAAQ;AAEtD;AC5BO,eAAeC,cACpB,WAA2B;AAE3B,MAAI,CAAC,WAAW;AACd,UAAMP,eAAc;MAAM;;IAAA;EAC3B;AAED,MAAI,CAAC,UAAU,gBAAgB;AAC7B,UAAM,kBAAkB,SAAS;EAClC;AAED,SAAO,oBAAoB,SAAS;AACtC;ACRgB,SAAAQ,YACd,WACA,gBAAiE;AAEjE,MAAI,CAAC,WAAW;AACd,UAAMR,eAAc;MAAM;;IAAA;EAC3B;AAED,YAAU,mBAAmB;AAE7B,SAAO,MAAK;AACV,cAAU,mBAAmB;EAC/B;AACF;ACMgB,SAAA,qBAAqB,MAAmB,OAAM,GAAE;AAK9D,oBAAiB,EAAG,KAClB,iBAAc;AAEZ,QAAI,CAAC,aAAa;AAChB,YAAMA,eAAc;QAAM;;MAAA;IAC3B;KAEH,OAAI;AAEF,UAAMA,eAAc;MAAM;;IAAA;EAC5B,CAAC;AAEH,SAAO,aAAa,mBAAmB,GAAG,GAAG,WAAW,EAAE,aAAY;AACxE;AA4CO,eAAeI,UACpB,WACA,SAAyB;AAEzB,cAAY,mBAAmB,SAAS;AACxC,SAAOK,WAAU,WAA+B,OAAO;AACzD;AAYM,SAAU,YAAY,WAAoB;AAC9C,cAAY,mBAAmB,SAAS;AACxC,SAAOC,cAAa,SAA6B;AACnD;AAegB,SAAA,UACd,WACA,gBAAiE;AAEjE,cAAY,mBAAmB,SAAS;AACxC,SAAOC,YAAW,WAA+B,cAAc;AACjE;AC1GA,0BAAyB;", "names": ["installationEntry", "MessageType", "db", "DATABASE_NAME", "DATABASE_VERSION", "OBJECT_STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "<PERSON><PERSON><PERSON>", "ERROR_FACTORY", "getHeaders", "extractAppConfig", "getMissingValueError", "getToken", "name", "version", "deleteToken", "onMessage", "_getToken", "_deleteToken", "_onMessage"]}