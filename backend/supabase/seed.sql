-- Seed data for local development
-- This file is used to populate the database with initial data

-- Insert some test data if needed
-- For now, we'll keep it minimal to avoid conflicts

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create a test user profile (this will be created by the trigger when a user signs up)
-- No need to insert test data here as the app will create users dynamically

-- Log that seeding is complete
SELECT 'Database seeding completed successfully' as message;
